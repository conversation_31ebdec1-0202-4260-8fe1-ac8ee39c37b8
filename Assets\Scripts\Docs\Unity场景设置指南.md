# Unity场景设置指南

## 概述
本文档描述如何在Unity中设置游戏化力量跟随系统的场景结构和组件配置。

## 场景结构

### 1. 主场景 (MainScene)

#### 层级结构
```
MainScene
├── GameManager (GameManager.cs)
├── Canvas (UI根节点)
│   ├── MainMenuUI (MainMenuUI.cs)
│   ├── DeviceConnectionUI (DeviceConnectionUI.cs)
│   ├── MVCCalibrationUI (MVCCalibrationGame.cs)
│   ├── RESTCalibrationUI (RESTCalibrationGame.cs)
│   ├── GameplayUI (GameplayUI.cs)
│   └── LevelTransitionUI (LevelTransitionUI.cs)
├── SystemManagers
│   ├── GameStateManager (GameStateManager.cs)
│   ├── BLEDeviceManager (BLEDeviceManager.cs)
│   ├── LevelManager (LevelManager.cs)
│   ├── PerformanceAnalyzer (PerformanceAnalyzer.cs)
│   └── DataManager (DataManager.cs)
├── GameWorld
│   ├── Main Camera
│   ├── PlayerBird (游戏中的小鸟对象)
│   ├── TargetPath (LineRenderer)
│   └── Effects (粒子特效)
└── Audio
    └── AudioManager (音效管理)
```

## 组件配置

### 1. GameManager 配置

**GameObject**: GameManager
**Script**: GameManager.cs

**必需引用**:
- MainMenuUI: 主菜单UI组件
- DeviceConnectionUI: 设备连接UI组件
- MVCCalibrationGame: MVC校准UI组件
- RESTCalibrationGame: REST校准UI组件
- GameplayUI: 游戏界面UI组件
- LevelTransitionUI: 关卡间UI组件

**配置参数**:
- Auto Start In Editor: true (Editor模式自动开始)
- Debug Participant Id: "DebugUser"

### 2. Canvas 设置

**Canvas 配置**:
- Render Mode: Screen Space - Overlay
- Canvas Scaler: Scale With Screen Size
- Reference Resolution: 1920x1080
- Screen Match Mode: Match Width Or Height
- Match: 0.5

### 3. MainMenuUI 配置

**UI元素**:
- Title Text: 游戏标题显示
- Participant Id Input: 参与者ID输入框
- Start Game Button: 开始游戏按钮
- Settings Button: 设置按钮
- Exit Button: 退出按钮
- Device Status Text: 设备状态显示
- Version Text: 版本信息显示
- Device Status Indicator: 设备状态指示器(Image)

### 4. DeviceConnectionUI 配置

**UI元素**:
- Status Text: 连接状态文字
- Device Info Text: 设备信息显示
- Connect Button: 连接按钮
- Skip Button: 跳过按钮(调试用)
- Back Button: 返回按钮
- Connection Progress: 连接进度条(Slider)
- Status Indicator: 状态指示器(Image)
- Connection Effect: 连接特效(ParticleSystem)

### 5. MVCCalibrationGame 配置

**UI元素**:
- Instruction Text: 指导文字
- Countdown Text: 倒计时显示
- Force Progress Bar: 力量进度条(Slider)
- Current Force Text: 当前力量显示
- Max Force Text: 最大力量显示
- Start Button: 开始按钮
- Encouragement Effect: 鼓励特效(ParticleSystem)

**音效配置**:
- Audio Source: 音效播放器
- Countdown Beep: 倒计时音效
- Start Sound: 开始音效
- New Record Sound: 新记录音效
- Completion Sound: 完成音效

### 6. RESTCalibrationGame 配置

**UI元素**:
- Instruction Text: 指导文字
- Stability Bar: 稳定性进度条(Slider)
- Force Value Text: 当前力量值显示
- Stability Indicator: 稳定性指示器(Image)
- Start Button: 开始按钮
- Relaxation Effect: 放松特效(ParticleSystem)

### 7. GameplayUI 配置

**游戏视觉元素**:
- Game Area: 游戏区域(RectTransform)
- Target Path: 目标轨迹线(LineRenderer)
- Player Bird: 玩家小鸟(Transform)
- Success Effect: 成功特效(ParticleSystem)
- Trail Effect: 轨迹特效(ParticleSystem)

**UI元素**:
- Level Name Text: 关卡名称
- Score Text: 当前得分
- Progress Bar: 进度条(Slider)
- Force Bar: 当前力量条(Slider)
- Time Remaining Text: 剩余时间
- Force Value Text: 力量数值显示
- Accuracy Indicator: 精度指示器(Image)

**配置参数**:
- Bird Smoothness: 10 (小鸟移动平滑度)
- Rotation Smoothness: 5 (旋转平滑度)
- Max Rotation: 30 (最大旋转角度)
- Path Points: 100 (轨迹点数)
- Path Width: 0.1 (轨迹宽度)

### 8. LevelTransitionUI 配置

**结果显示**:
- Level Complete Text: 关卡完成文字
- Final Score Text: 最终得分
- Accuracy Text: 跟随精度
- Consistency Text: 稳定性
- Stars Display: 星级显示(Image数组，3个星星)

**下一关预览**:
- Next Level Name Text: 下一关名称
- Next Level Desc Text: 下一关描述
- Next Level Preview: 下一关预览(Image)

**操作按钮**:
- Continue Button: 继续下一关
- Rest Button: 休息一会
- Quit Button: 结束游戏

**动画设置**:
- Score Animation Duration: 1.5
- Percentage Animation Duration: 1.0
- Star Animation Delay: 0.3

## 系统管理器配置

### 1. GameStateManager
- Enable Debug Logs: true

### 2. BLEDeviceManager
- Target Device MAC: "3C:AB:72:6F:68:6D"
- Notify Char UUID: "9ECADC24-0EE5-A9E0-93F3-A3B50300406E"
- Smoothing Factor: 0.8
- Enable Data Logging: true

### 3. LevelManager
- Enable Debug Logs: true
- 预定义关卡会自动初始化

### 4. PerformanceAnalyzer
- Tolerance Threshold: 10
- Response Time Window: 2
- Enable Detailed Analysis: true
- Enable Debug Logs: true

### 5. DataManager
- Data Folder Name: "ForceGameData"
- Auto Save: true
- Auto Save Interval: 30
- Enable Debug Logs: true

## 游戏世界配置

### 1. Main Camera
- Position: (0, 0, -10)
- Projection: Orthographic
- Size: 5
- Background: 深蓝色或黑色

### 2. PlayerBird
- 可以使用简单的Sprite或3D模型
- 添加Trail Renderer组件用于轨迹效果
- Position: 游戏区域左侧

### 3. TargetPath (LineRenderer)
- Material: 明亮的材质
- Width: 0.1
- Color: 白色到灰色渐变
- Use World Space: false

### 4. 粒子特效
- Success Effect: 绿色粒子，成功时播放
- Trail Effect: 跟随小鸟的轨迹特效
- Connection Effect: 设备连接时的特效
- Relaxation Effect: REST校准时的放松特效

## 音效配置

### AudioManager 设置
- 背景音乐: 轻松的环境音乐
- 音效: 按钮点击、成功提示、错误提示等
- 音量控制: 可在设置中调节

### 音效文件
- countdown_beep.wav: 倒计时音效
- start_sound.wav: 开始音效
- success_sound.wav: 成功音效
- error_sound.wav: 错误音效
- star_sound.wav: 获得星星音效
- completion_sound.wav: 完成音效

## 输入系统

### 触摸输入 (移动设备)
- 支持触摸屏操作
- UI按钮响应触摸事件

### 键盘输入 (PC调试)
- Space: 模拟握力输入
- R: 重置当前关卡
- N: 跳到下一关
- ESC: 返回主菜单

## 构建设置

### Android 构建
- Minimum API Level: 21 (Android 5.0)
- Target API Level: 最新
- 权限: BLUETOOTH, BLUETOOTH_ADMIN, ACCESS_FINE_LOCATION

### Windows 构建
- Architecture: x86_64
- Scripting Backend: Mono

## 调试功能

### Editor 模式
- 自动启用BLE模拟模式
- 自动开始游戏流程
- 提供调试按钮和快捷键

### 日志系统
- 所有主要组件都有调试日志
- 可通过Inspector面板控制日志开关

### 性能监控
- 实时显示FPS
- 内存使用监控
- 数据传输统计

## 注意事项

1. **UI适配**: 确保UI在不同分辨率下正确显示
2. **性能优化**: 合理使用对象池管理粒子特效
3. **数据安全**: 定期保存游戏数据，防止意外丢失
4. **错误处理**: 所有网络和设备操作都要有错误处理
5. **用户体验**: 提供清晰的视觉反馈和音效提示

## 测试建议

1. **功能测试**: 测试所有UI交互和状态转换
2. **设备测试**: 在不同设备上测试BLE连接
3. **性能测试**: 长时间运行测试内存泄漏
4. **用户测试**: 邀请用户测试游戏体验

这个设置指南提供了完整的Unity场景配置信息，按照这个指南可以正确设置游戏化力量跟随系统。
