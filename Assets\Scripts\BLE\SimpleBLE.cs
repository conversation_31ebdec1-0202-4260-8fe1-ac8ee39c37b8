using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using UnityEngine;

/// <summary>
/// Cross-platform BLE wrapper class (based on SimpleBLE)
/// </summary>
public static class SimpleBLE
{
    #region Public API

    /// <summary>
    /// BLE device information structure
    /// </summary>
    public struct BLEDeviceInfo
    {
        public string Identifier;
        public string Address;
        public string Name;
        public short RSSI;
        public bool Connectable;
        public bool Connected;
    }

    /// <summary>
    /// BLE service information structure
    /// </summary>
    public struct BLEServiceInfo
    {
        public string UUID;
        public List<BLECharacteristicInfo> Characteristics;
    }

    /// <summary>
    /// BLE characteristic information structure
    /// </summary>
    public struct BLECharacteristicInfo
    {
        public string UUID;
        public bool CanRead;
        public bool CanWrite;
        public bool CanNotify;
        public bool CanIndicate;
        public bool CanWriteWithoutResponse;
    }

    // Event delegates
    public delegate void OnDeviceDiscoveredHandler(BLEDeviceInfo device);
    public delegate void OnDeviceConnectedHandler(string deviceId);
    public delegate void OnDeviceDisconnectedHandler(string deviceId);
    public delegate void OnServicesDiscoveredHandler(string deviceId, List<BLEServiceInfo> services);
    public delegate void OnCharacteristicValueChangedHandler(string deviceId, string serviceUuid, string characteristicUuid, byte[] value);
    public delegate void OnErrorHandler(string message);

    // Public events
    public static event OnDeviceDiscoveredHandler OnDeviceDiscovered;
    public static event OnDeviceConnectedHandler OnDeviceConnected;
    public static event OnDeviceDisconnectedHandler OnDeviceDisconnected;
    public static event OnServicesDiscoveredHandler OnServicesDiscovered;
    public static event OnCharacteristicValueChangedHandler OnCharacteristicValueChanged;
    public static event OnErrorHandler OnError;

    /// <summary>
    /// Initialize BLE system
    /// </summary>
    public static bool Initialize()
    {
        if (_initialized) return true;

        try
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            return AndroidInitialize();
#elif UNITY_STANDALONE_WIN || UNITY_EDITOR
            return WindowsInitialize();
#else
            OnError?.Invoke("Platform not supported");
            return false;
#endif
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Initialization failed: {e.Message}");
            return false;
        }
    }

    /// <summary>
    /// Start scanning for BLE devices
    /// </summary>
    public static bool StartScan()
    {
        if (!Initialize()) return false;

        try
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            return AndroidStartScan();
#elif UNITY_STANDALONE_WIN || UNITY_EDITOR
            return WindowsStartScan();
#else
            return false;
#endif
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Start scan failed: {e.Message}");
            return false;
        }
    }

    /// <summary>
    /// Stop scanning for BLE devices
    /// </summary>
    public static bool StopScan()
    {
        try
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            return AndroidStopScan();
#elif UNITY_STANDALONE_WIN || UNITY_EDITOR
            return WindowsStopScan();
#else
            return false;
#endif
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Stop scan failed: {e.Message}");
            return false;
        }
    }

    /// <summary>
    /// Connect to a BLE device
    /// </summary>
    /// <param name="deviceId">Device identifier</param>
    public static bool ConnectDevice(string deviceId)
    {
        if (string.IsNullOrEmpty(deviceId)) return false;

        try
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            return AndroidConnectDevice(deviceId);
#elif UNITY_STANDALONE_WIN || UNITY_EDITOR
            return WindowsConnectDevice(deviceId);
#else
            return false;
#endif
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Connect failed: {e.Message}");
            return false;
        }
    }

    /// <summary>
    /// Disconnect from a BLE device
    /// </summary>
    /// <param name="deviceId">Device identifier</param>
    public static bool DisconnectDevice(string deviceId)
    {
        if (string.IsNullOrEmpty(deviceId)) return false;

        try
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            return AndroidDisconnectDevice(deviceId);
#elif UNITY_STANDALONE_WIN || UNITY_EDITOR
            return WindowsDisconnectDevice(deviceId);
#else
            return false;
#endif
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Disconnect failed: {e.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// 发现设备服务
    /// </summary>
    /// <param name="deviceId">设备标识符</param>
    public static void DiscoverServices(string deviceId)
    {
#if UNITY_ANDROID && !UNITY_EDITOR
        AndroidDiscoverServices(deviceId);
#elif UNITY_STANDALONE_WIN || UNITY_EDITOR
        WindowsDiscoverServices(deviceId);
#endif
    }

    /// <summary>
    /// Read characteristic value
    /// </summary>
    /// <param name="deviceId">Device identifier</param>
    /// <param name="serviceUuid">Service UUID</param>
    /// <param name="characteristicUuid">Characteristic UUID</param>
    public static byte[] ReadCharacteristic(string deviceId, string serviceUuid, string characteristicUuid)
    {
        if (string.IsNullOrEmpty(deviceId) || string.IsNullOrEmpty(serviceUuid) || string.IsNullOrEmpty(characteristicUuid))
            return null;

        try
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            return AndroidReadCharacteristic(deviceId, serviceUuid, characteristicUuid);
#elif UNITY_STANDALONE_WIN || UNITY_EDITOR
            return WindowsReadCharacteristic(deviceId, serviceUuid, characteristicUuid);
#else
            return null;
#endif
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Read characteristic failed: {e.Message}");
            return null;
        }
    }

    /// <summary>
    /// Write characteristic value
    /// </summary>
    /// <param name="deviceId">Device identifier</param>
    /// <param name="serviceUuid">Service UUID</param>
    /// <param name="characteristicUuid">Characteristic UUID</param>
    /// <param name="data">Data to write</param>
    /// <param name="withResponse">Whether to require response</param>
    public static bool WriteCharacteristic(string deviceId, string serviceUuid, string characteristicUuid, byte[] data, bool withResponse = true)
    {
        if (string.IsNullOrEmpty(deviceId) || string.IsNullOrEmpty(serviceUuid) || string.IsNullOrEmpty(characteristicUuid) || data == null)
            return false;

        try
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            return AndroidWriteCharacteristic(deviceId, serviceUuid, characteristicUuid, data, withResponse);
#elif UNITY_STANDALONE_WIN || UNITY_EDITOR
            return WindowsWriteCharacteristic(deviceId, serviceUuid, characteristicUuid, data, withResponse);
#else
            return false;
#endif
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Write characteristic failed: {e.Message}");
            return false;
        }
    }

    /// <summary>
    /// Subscribe to characteristic notifications
    /// </summary>
    /// <param name="deviceId">Device identifier</param>
    /// <param name="serviceUuid">Service UUID</param>
    /// <param name="characteristicUuid">Characteristic UUID</param>
    public static bool SubscribeCharacteristic(string deviceId, string serviceUuid, string characteristicUuid)
    {
        if (string.IsNullOrEmpty(deviceId) || string.IsNullOrEmpty(serviceUuid) || string.IsNullOrEmpty(characteristicUuid))
            return false;

        try
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            return AndroidSubscribeCharacteristic(deviceId, serviceUuid, characteristicUuid);
#elif UNITY_STANDALONE_WIN || UNITY_EDITOR
            return WindowsSubscribeCharacteristic(deviceId, serviceUuid, characteristicUuid);
#else
            return false;
#endif
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Subscribe failed: {e.Message}");
            return false;
        }
    }

    /// <summary>
    /// Unsubscribe from characteristic notifications
    /// </summary>
    /// <param name="deviceId">Device identifier</param>
    /// <param name="serviceUuid">Service UUID</param>
    /// <param name="characteristicUuid">Characteristic UUID</param>
    public static bool UnsubscribeCharacteristic(string deviceId, string serviceUuid, string characteristicUuid)
    {
        if (string.IsNullOrEmpty(deviceId) || string.IsNullOrEmpty(serviceUuid) || string.IsNullOrEmpty(characteristicUuid))
            return false;

        try
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            return AndroidUnsubscribeCharacteristic(deviceId, serviceUuid, characteristicUuid);
#elif UNITY_STANDALONE_WIN || UNITY_EDITOR
            return WindowsUnsubscribeCharacteristic(deviceId, serviceUuid, characteristicUuid);
#else
            return false;
#endif
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Unsubscribe failed: {e.Message}");
            return false;
        }
    }

    /// <summary>
    /// Get list of discovered devices
    /// </summary>
    public static List<BLEDeviceInfo> GetDiscoveredDevices()
    {
#if UNITY_ANDROID && !UNITY_EDITOR
        return AndroidGetDiscoveredDevices();
#elif UNITY_STANDALONE_WIN || UNITY_EDITOR
        return WindowsGetDiscoveredDevices();
#else
        return new List<BLEDeviceInfo>();
#endif
    }

    /// <summary>
    /// Cleanup and release resources
    /// </summary>
    public static void Cleanup()
    {
        try
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            AndroidCleanup();
#elif UNITY_STANDALONE_WIN || UNITY_EDITOR
            WindowsCleanup();
#endif
            _initialized = false;
        }
        catch (Exception e)
        {
            Debug.LogError($"Cleanup failed: {e.Message}");
        }
    }

    #endregion

    #region 内部实现 - Android

#if UNITY_ANDROID && !UNITY_EDITOR
    
    private static AndroidJavaObject _bleAdapter;
    private static AndroidJavaObject _scanCallback;
    private static AndroidJavaObject _connectionCallback;
    private static Dictionary<string, AndroidJavaObject> _connectedDevices = new Dictionary<string, AndroidJavaObject>();

    private static void AndroidInitialize()
    {
        try
        {
            // 获取 Unity 活动上下文
            AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            AndroidJavaObject activity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
            
            // 初始化 SimpleBLE
            AndroidJavaClass bleClass = new AndroidJavaClass("com.simpleble.SimpleBLE");
            _bleAdapter = bleClass.CallStatic<AndroidJavaObject>("getAdapter", activity);
            
            // 创建扫描回调
            _scanCallback = new AndroidJavaObject("com.simpleble.SimpleBLEScanCallback");
            _scanCallback.Call("setCallback", new AndroidScanCallback());
            
            // 创建连接回调
            _connectionCallback = new AndroidJavaObject("com.simpleble.SimpleBLEConnectionCallback");
            _connectionCallback.Call("setCallback", new AndroidConnectionCallback());
            
            Debug.Log("SimpleBLE Android initialized successfully");
        }
        catch (Exception e)
        {
            Debug.LogError($"SimpleBLE Android initialization failed: {e.Message}");
            OnError?.Invoke($"Android init error: {e.Message}");
        }
    }

    private static void AndroidStartScan()
    {
        try
        {
            _bleAdapter.Call("startScan", _scanCallback);
            Debug.Log("BLE scan started on Android");
        }
        catch (Exception e)
        {
            Debug.LogError($"Android scan start failed: {e.Message}");
            OnError?.Invoke($"Scan error: {e.Message}");
        }
    }

    private static void AndroidStopScan()
    {
        try
        {
            _bleAdapter.Call("stopScan");
            Debug.Log("BLE scan stopped on Android");
        }
        catch (Exception e)
        {
            Debug.LogError($"Android scan stop failed: {e.Message}");
        }
    }

    private static void AndroidConnectDevice(string deviceId)
    {
        try
        {
            _bleAdapter.Call("connectDevice", deviceId, _connectionCallback);
        }
        catch (Exception e)
        {
            Debug.LogError($"Android connect failed: {e.Message}");
            OnError?.Invoke($"Connect error: {e.Message}");
        }
    }

    // 其他Android实现方法...

    // Android回调类
    private class AndroidScanCallback : AndroidJavaProxy
    {
        public AndroidScanCallback() : base("com.simpleble.SimpleBLEScanCallbackInterface") { }

        public void onDeviceFound(AndroidJavaObject device)
        {
            BLEDeviceInfo info = new BLEDeviceInfo
            {
                Identifier = device.Call<string>("getIdentifier"),
                Address = device.Call<string>("getAddress"),
                Name = device.Call<string>("getName"),
                RSSI = device.Call<int>("getRSSI"),
                Connectable = device.Call<bool>("isConnectable")
            };
            
            OnDeviceDiscovered?.Invoke(info);
        }
    }

    private class AndroidConnectionCallback : AndroidJavaProxy
    {
        public AndroidConnectionCallback() : base("com.simpleble.SimpleBLEConnectionCallbackInterface") { }

        public void onConnected(AndroidJavaObject device)
        {
            string deviceId = device.Call<string>("getIdentifier");
            _connectedDevices[deviceId] = device;
            OnDeviceConnected?.Invoke(deviceId);
        }

        public void onDisconnected(AndroidJavaObject device)
        {
            string deviceId = device.Call<string>("getIdentifier");
            _connectedDevices.Remove(deviceId);
            OnDeviceDisconnected?.Invoke(deviceId);
        }

        public void onServicesDiscovered(AndroidJavaObject device, AndroidJavaObject servicesList)
        {
            string deviceId = device.Call<string>("getIdentifier");
            List<BLEServiceInfo> services = new List<BLEServiceInfo>();
            
            int size = servicesList.Call<int>("size");
            for (int i = 0; i < size; i++)
            {
                AndroidJavaObject serviceObj = servicesList.Call<AndroidJavaObject>("get", i);
                BLEServiceInfo service = new BLEServiceInfo
                {
                    UUID = serviceObj.Call<string>("getUuid"),
                    Characteristics = new List<BLECharacteristicInfo>()
                };
                
                AndroidJavaObject characteristicsList = serviceObj.Call<AndroidJavaObject>("getCharacteristics");
                int charSize = characteristicsList.Call<int>("size");
                for (int j = 0; j < charSize; j++)
                {
                    AndroidJavaObject charObj = characteristicsList.Call<AndroidJavaObject>("get", j);
                    BLECharacteristicInfo characteristic = new BLECharacteristicInfo
                    {
                        UUID = charObj.Call<string>("getUuid"),
                        CanRead = charObj.Call<bool>("canRead"),
                        CanWrite = charObj.Call<bool>("canWrite"),
                        CanNotify = charObj.Call<bool>("canNotify")
                    };
                    service.Characteristics.Add(characteristic);
                }
                
                services.Add(service);
            }
            
            OnServicesDiscovered?.Invoke(deviceId, services);
        }
    }
    
#endif

    #endregion

    #region Windows Implementation

#if UNITY_STANDALONE_WIN || UNITY_EDITOR

    // SimpleBLE error codes
    public enum SimpleBLEError : int
    {
        SIMPLEBLE_SUCCESS = 0,
        SIMPLEBLE_FAILURE = 1
    }

    // SimpleBLE UUID structure
    [StructLayout(LayoutKind.Sequential)]
    public struct SimpleBLEUUID
    {
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 37)]
        public string value;
    }

    // Windows DLL imports - Adapter functions
    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern bool simpleble_adapter_is_bluetooth_enabled();

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern UIntPtr simpleble_adapter_get_count();

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern IntPtr simpleble_adapter_get_handle(UIntPtr index);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern void simpleble_adapter_release_handle(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern IntPtr simpleble_adapter_identifier(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern IntPtr simpleble_adapter_address(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_adapter_scan_start(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_adapter_scan_stop(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_adapter_scan_for(IntPtr handle, int timeout_ms);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_adapter_scan_is_active(IntPtr handle, out bool active);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern UIntPtr simpleble_adapter_scan_get_results_count(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern IntPtr simpleble_adapter_scan_get_results_handle(IntPtr handle, UIntPtr index);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_adapter_set_callback_on_scan_found(
        IntPtr handle, ScanFoundCallback callback, IntPtr userdata);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_adapter_set_callback_on_scan_updated(
        IntPtr handle, ScanUpdatedCallback callback, IntPtr userdata);

    // Windows DLL imports - Peripheral functions
    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern void simpleble_peripheral_release_handle(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern IntPtr simpleble_peripheral_identifier(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern IntPtr simpleble_peripheral_address(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern short simpleble_peripheral_rssi(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern short simpleble_peripheral_tx_power(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern ushort simpleble_peripheral_mtu(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_is_connectable(IntPtr handle, out bool connectable);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_is_connected(IntPtr handle, out bool connected);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_is_paired(IntPtr handle, out bool paired);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_connect(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_disconnect(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_unpair(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern void simpleble_free(IntPtr ptr);

    // Windows DLL imports - Service and Characteristic functions
    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern UIntPtr simpleble_peripheral_services_count(IntPtr handle);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_services_get(IntPtr handle, UIntPtr index, out SimpleBLEService service);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_read(IntPtr handle, SimpleBLEUUID service, SimpleBLEUUID characteristic, out IntPtr data, out UIntPtr data_length);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_write_request(IntPtr handle, SimpleBLEUUID service, SimpleBLEUUID characteristic, IntPtr data, UIntPtr data_length);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_write_command(IntPtr handle, SimpleBLEUUID service, SimpleBLEUUID characteristic, IntPtr data, UIntPtr data_length);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_notify(IntPtr handle, SimpleBLEUUID service, SimpleBLEUUID characteristic, NotificationCallback callback, IntPtr userdata);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_indicate(IntPtr handle, SimpleBLEUUID service, SimpleBLEUUID characteristic, NotificationCallback callback, IntPtr userdata);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_unsubscribe(IntPtr handle, SimpleBLEUUID service, SimpleBLEUUID characteristic);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_set_callback_on_connected(IntPtr handle, PeripheralConnectedCallback callback, IntPtr userdata);

    [DllImport("simpleble-c", CallingConvention = CallingConvention.Cdecl)]
    private static extern SimpleBLEError simpleble_peripheral_set_callback_on_disconnected(IntPtr handle, PeripheralDisconnectedCallback callback, IntPtr userdata);

    // Service structure
    [StructLayout(LayoutKind.Sequential)]
    public struct SimpleBLEService
    {
        public SimpleBLEUUID uuid;
        public UIntPtr characteristic_count;
        public IntPtr characteristics; // Pointer to array of SimpleBLECharacteristic
    }

    // Characteristic structure
    [StructLayout(LayoutKind.Sequential)]
    public struct SimpleBLECharacteristic
    {
        public SimpleBLEUUID uuid;
        public UIntPtr descriptor_count;
        public IntPtr descriptors; // Pointer to array of SimpleBLEDescriptor
        public UIntPtr capability_count;
        public IntPtr capabilities; // Pointer to array of strings
    }

    // Callback delegates
    private delegate void ScanFoundCallback(IntPtr adapter, IntPtr peripheral, IntPtr userdata);
    private delegate void ScanUpdatedCallback(IntPtr adapter, IntPtr peripheral, IntPtr userdata);
    private delegate void PeripheralConnectedCallback(IntPtr peripheral, IntPtr userdata);
    private delegate void PeripheralDisconnectedCallback(IntPtr peripheral, IntPtr userdata);
    private delegate void NotificationCallback(IntPtr peripheral, SimpleBLEUUID service, SimpleBLEUUID characteristic, IntPtr data, UIntPtr data_length, IntPtr userdata);

    // Windows implementation variables
    private static IntPtr _adapterHandle = IntPtr.Zero;
    private static bool _initializedWindows = false;
    private static ScanFoundCallback _scanFoundCallback;
    private static ScanUpdatedCallback _scanUpdatedCallback;
    private static Dictionary<string, IntPtr> _discoveredDevices = new Dictionary<string, IntPtr>();
    private static Dictionary<string, IntPtr> _connectedDevices = new Dictionary<string, IntPtr>();

    private static bool WindowsInitialize()
    {
        if (_initializedWindows) return true;

        try
        {
            // Check if Bluetooth is enabled
            if (!simpleble_adapter_is_bluetooth_enabled())
            {
                OnError?.Invoke("Bluetooth is not enabled");
                return false;
            }

            // Get adapter count
            UIntPtr adapterCount = simpleble_adapter_get_count();
            if (adapterCount.ToUInt32() == 0)
            {
                OnError?.Invoke("No BLE adapters found");
                return false;
            }

            // Get first adapter
            _adapterHandle = simpleble_adapter_get_handle(UIntPtr.Zero);
            if (_adapterHandle == IntPtr.Zero)
            {
                OnError?.Invoke("Failed to get adapter handle");
                return false;
            }

            // Set up callbacks
            _scanFoundCallback = OnDeviceFoundWindows;
            _scanUpdatedCallback = OnDeviceUpdatedWindows;

            var result1 = simpleble_adapter_set_callback_on_scan_found(_adapterHandle, _scanFoundCallback, IntPtr.Zero);
            var result2 = simpleble_adapter_set_callback_on_scan_updated(_adapterHandle, _scanUpdatedCallback, IntPtr.Zero);

            if (result1 != SimpleBLEError.SIMPLEBLE_SUCCESS || result2 != SimpleBLEError.SIMPLEBLE_SUCCESS)
            {
                OnError?.Invoke("Failed to set scan callbacks");
                return false;
            }

            _initializedWindows = true;
            _initialized = true;
            Debug.Log("SimpleBLE Windows initialized successfully");
            return true;
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Windows initialization failed: {e.Message}");
            return false;
        }
    }

    private static bool WindowsStartScan()
    {
        if (!_initializedWindows || _adapterHandle == IntPtr.Zero) return false;

        try
        {
            var result = simpleble_adapter_scan_start(_adapterHandle);
            if (result == SimpleBLEError.SIMPLEBLE_SUCCESS)
            {
                Debug.Log("BLE scan started on Windows");
                return true;
            }
            else
            {
                OnError?.Invoke("Failed to start scan");
                return false;
            }
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Scan start error: {e.Message}");
            return false;
        }
    }

    private static bool WindowsStopScan()
    {
        if (!_initializedWindows || _adapterHandle == IntPtr.Zero) return false;

        try
        {
            var result = simpleble_adapter_scan_stop(_adapterHandle);
            if (result == SimpleBLEError.SIMPLEBLE_SUCCESS)
            {
                Debug.Log("BLE scan stopped on Windows");
                return true;
            }
            else
            {
                OnError?.Invoke("Failed to stop scan");
                return false;
            }
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Scan stop error: {e.Message}");
            return false;
        }
    }

    // Windows device discovery callbacks
    [AOT.MonoPInvokeCallback(typeof(ScanFoundCallback))]
    private static void OnDeviceFoundWindows(IntPtr adapter, IntPtr peripheral, IntPtr userdata)
    {
        try
        {
            // Get device information
            string identifier = MarshalString(simpleble_peripheral_identifier(peripheral));
            string address = MarshalString(simpleble_peripheral_address(peripheral));
            short rssi = simpleble_peripheral_rssi(peripheral);

            // Check if connectable
            bool connectable = false;
            if (simpleble_peripheral_is_connectable(peripheral, out connectable) != SimpleBLEError.SIMPLEBLE_SUCCESS)
            {
                connectable = false;
            }

            // Check if connected
            bool connected = false;
            if (simpleble_peripheral_is_connected(peripheral, out connected) != SimpleBLEError.SIMPLEBLE_SUCCESS)
            {
                connected = false;
            }

            BLEDeviceInfo info = new BLEDeviceInfo
            {
                Identifier = identifier,
                Address = address,
                Name = string.IsNullOrEmpty(identifier) ? "Unknown Device" : identifier,
                RSSI = rssi,
                Connectable = connectable,
                Connected = connected
            };

            // Store device handle for later use
            if (!string.IsNullOrEmpty(identifier))
            {
                _discoveredDevices[identifier] = peripheral;
            }

            // Ensure event is triggered on main thread
            UnityMainThreadDispatcher.Enqueue(() => OnDeviceDiscovered?.Invoke(info));
        }
        catch (Exception e)
        {
            Debug.LogError($"Error processing discovered device: {e.Message}");
        }
    }

    [AOT.MonoPInvokeCallback(typeof(ScanUpdatedCallback))]
    private static void OnDeviceUpdatedWindows(IntPtr adapter, IntPtr peripheral, IntPtr userdata)
    {
        // Handle device updates (RSSI changes, etc.)
        OnDeviceFoundWindows(adapter, peripheral, userdata);
    }

    // Helper method: Handle string return values
    private static string MarshalString(IntPtr strPtr)
    {
        if (strPtr == IntPtr.Zero)
            return string.Empty;

        string result = Marshal.PtrToStringAnsi(strPtr);
        simpleble_free(strPtr); // Free memory
        return result ?? string.Empty;
    }

    // Helper method: Create SimpleBLE UUID from string
    private static SimpleBLEUUID CreateUUID(string uuidString)
    {
        return new SimpleBLEUUID { value = uuidString };
    }

    private static bool WindowsConnectDevice(string deviceId)
    {
        if (!_initializedWindows || string.IsNullOrEmpty(deviceId)) return false;

        try
        {
            if (!_discoveredDevices.TryGetValue(deviceId, out IntPtr peripheralHandle))
            {
                OnError?.Invoke("Device not found in discovered devices");
                return false;
            }

            // Set connection callbacks
            var connectedCallback = new PeripheralConnectedCallback(OnDeviceConnectedWindows);
            var disconnectedCallback = new PeripheralDisconnectedCallback(OnDeviceDisconnectedWindows);

            simpleble_peripheral_set_callback_on_connected(peripheralHandle, connectedCallback, IntPtr.Zero);
            simpleble_peripheral_set_callback_on_disconnected(peripheralHandle, disconnectedCallback, IntPtr.Zero);

            // Connect to device
            var result = simpleble_peripheral_connect(peripheralHandle);
            if (result == SimpleBLEError.SIMPLEBLE_SUCCESS)
            {
                _connectedDevices[deviceId] = peripheralHandle;
                return true;
            }
            else
            {
                OnError?.Invoke($"Failed to connect to device: {deviceId}");
                return false;
            }
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Connect error: {e.Message}");
            return false;
        }
    }
    
    private static void WindowsDiscoverServices(string deviceId)
    {
        if (!_initializedWindows) return;
    }

    private static bool WindowsDisconnectDevice(string deviceId)
    {
        if (!_initializedWindows || string.IsNullOrEmpty(deviceId)) return false;

        try
        {
            if (!_connectedDevices.TryGetValue(deviceId, out IntPtr peripheralHandle))
            {
                OnError?.Invoke("Device not connected");
                return false;
            }

            var result = simpleble_peripheral_disconnect(peripheralHandle);
            if (result == SimpleBLEError.SIMPLEBLE_SUCCESS)
            {
                _connectedDevices.Remove(deviceId);
                return true;
            }
            else
            {
                OnError?.Invoke($"Failed to disconnect device: {deviceId}");
                return false;
            }
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Disconnect error: {e.Message}");
            return false;
        }
    }

    private static byte[] WindowsReadCharacteristic(string deviceId, string serviceUuid, string characteristicUuid)
    {
        if (!_initializedWindows || string.IsNullOrEmpty(deviceId)) return null;

        try
        {
            if (!_connectedDevices.TryGetValue(deviceId, out IntPtr peripheralHandle))
            {
                OnError?.Invoke("Device not connected");
                return null;
            }

            var service = CreateUUID(serviceUuid);
            var characteristic = CreateUUID(characteristicUuid);

            var result = simpleble_peripheral_read(peripheralHandle, service, characteristic, out IntPtr dataPtr, out UIntPtr dataLength);
            if (result == SimpleBLEError.SIMPLEBLE_SUCCESS && dataPtr != IntPtr.Zero)
            {
                byte[] data = new byte[dataLength.ToUInt32()];
                Marshal.Copy(dataPtr, data, 0, (int)dataLength.ToUInt32());
                simpleble_free(dataPtr);
                return data;
            }
            else
            {
                OnError?.Invoke($"Failed to read characteristic: {characteristicUuid}");
                return null;
            }
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Read error: {e.Message}");
            return null;
        }
    }

    private static bool WindowsWriteCharacteristic(string deviceId, string serviceUuid, string characteristicUuid, byte[] data, bool withResponse = true)
    {
        if (!_initializedWindows || string.IsNullOrEmpty(deviceId) || data == null) return false;

        try
        {
            if (!_connectedDevices.TryGetValue(deviceId, out IntPtr peripheralHandle))
            {
                OnError?.Invoke("Device not connected");
                return false;
            }

            var service = CreateUUID(serviceUuid);
            var characteristic = CreateUUID(characteristicUuid);

            // Allocate unmanaged memory for data
            IntPtr dataPtr = Marshal.AllocHGlobal(data.Length);
            Marshal.Copy(data, 0, dataPtr, data.Length);

            SimpleBLEError result;
            if (withResponse)
            {
                result = simpleble_peripheral_write_request(peripheralHandle, service, characteristic, dataPtr, new UIntPtr((uint)data.Length));
            }
            else
            {
                result = simpleble_peripheral_write_command(peripheralHandle, service, characteristic, dataPtr, new UIntPtr((uint)data.Length));
            }

            Marshal.FreeHGlobal(dataPtr);

            if (result == SimpleBLEError.SIMPLEBLE_SUCCESS)
            {
                return true;
            }
            else
            {
                OnError?.Invoke($"Failed to write characteristic: {characteristicUuid}");
                return false;
            }
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Write error: {e.Message}");
            return false;
        }
    }

    private static bool WindowsSubscribeCharacteristic(string deviceId, string serviceUuid, string characteristicUuid)
    {
        if (!_initializedWindows || string.IsNullOrEmpty(deviceId)) return false;

        try
        {
            if (!_connectedDevices.TryGetValue(deviceId, out IntPtr peripheralHandle))
            {
                OnError?.Invoke("Device not connected");
                return false;
            }

            var service = CreateUUID(serviceUuid);
            var characteristic = CreateUUID(characteristicUuid);

            var callback = new NotificationCallback(OnNotificationReceived);
            var result = simpleble_peripheral_notify(peripheralHandle, service, characteristic, callback, IntPtr.Zero);

            if (result == SimpleBLEError.SIMPLEBLE_SUCCESS)
            {
                return true;
            }
            else
            {
                OnError?.Invoke($"Failed to subscribe to characteristic: {characteristicUuid}");
                return false;
            }
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Subscribe error: {e.Message}");
            return false;
        }
    }

    private static bool WindowsUnsubscribeCharacteristic(string deviceId, string serviceUuid, string characteristicUuid)
    {
        if (!_initializedWindows || string.IsNullOrEmpty(deviceId)) return false;

        try
        {
            if (!_connectedDevices.TryGetValue(deviceId, out IntPtr peripheralHandle))
            {
                OnError?.Invoke("Device not connected");
                return false;
            }

            var service = CreateUUID(serviceUuid);
            var characteristic = CreateUUID(characteristicUuid);

            var result = simpleble_peripheral_unsubscribe(peripheralHandle, service, characteristic);

            if (result == SimpleBLEError.SIMPLEBLE_SUCCESS)
            {
                return true;
            }
            else
            {
                OnError?.Invoke($"Failed to unsubscribe from characteristic: {characteristicUuid}");
                return false;
            }
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Unsubscribe error: {e.Message}");
            return false;
        }
    }

    private static List<BLEDeviceInfo> WindowsGetDiscoveredDevices()
    {
        var devices = new List<BLEDeviceInfo>();

        if (!_initializedWindows || _adapterHandle == IntPtr.Zero) return devices;

        try
        {
            UIntPtr count = simpleble_adapter_scan_get_results_count(_adapterHandle);
            for (UIntPtr i = UIntPtr.Zero; i.ToUInt32() < count.ToUInt32(); i = new UIntPtr(i.ToUInt32() + 1))
            {
                IntPtr peripheralHandle = simpleble_adapter_scan_get_results_handle(_adapterHandle, i);
                if (peripheralHandle != IntPtr.Zero)
                {
                    string identifier = MarshalString(simpleble_peripheral_identifier(peripheralHandle));
                    string address = MarshalString(simpleble_peripheral_address(peripheralHandle));
                    short rssi = simpleble_peripheral_rssi(peripheralHandle);

                    bool connectable = false;
                    simpleble_peripheral_is_connectable(peripheralHandle, out connectable);

                    bool connected = false;
                    simpleble_peripheral_is_connected(peripheralHandle, out connected);

                    devices.Add(new BLEDeviceInfo
                    {
                        Identifier = identifier,
                        Address = address,
                        Name = string.IsNullOrEmpty(identifier) ? "Unknown Device" : identifier,
                        RSSI = rssi,
                        Connectable = connectable,
                        Connected = connected
                    });
                }
            }
        }
        catch (Exception e)
        {
            OnError?.Invoke($"Get discovered devices error: {e.Message}");
        }

        return devices;
    }

    private static void WindowsCleanup()
    {
        try
        {
            // Disconnect all connected devices
            foreach (var kvp in _connectedDevices.ToList())
            {
                simpleble_peripheral_disconnect(kvp.Value);
            }
            _connectedDevices.Clear();

            // Release discovered device handles
            _discoveredDevices.Clear();

            // Release adapter handle
            if (_adapterHandle != IntPtr.Zero)
            {
                simpleble_adapter_release_handle(_adapterHandle);
                _adapterHandle = IntPtr.Zero;
            }

            _initializedWindows = false;
        }
        catch (Exception e)
        {
            Debug.LogError($"Windows cleanup error: {e.Message}");
        }
    }

    // Connection callback implementations
    [AOT.MonoPInvokeCallback(typeof(PeripheralConnectedCallback))]
    private static void OnDeviceConnectedWindows(IntPtr peripheral, IntPtr userdata)
    {
        try
        {
            string identifier = MarshalString(simpleble_peripheral_identifier(peripheral));
            UnityMainThreadDispatcher.Enqueue(() => OnDeviceConnected?.Invoke(identifier));
        }
        catch (Exception e)
        {
            Debug.LogError($"Error in device connected callback: {e.Message}");
        }
    }

    [AOT.MonoPInvokeCallback(typeof(PeripheralDisconnectedCallback))]
    private static void OnDeviceDisconnectedWindows(IntPtr peripheral, IntPtr userdata)
    {
        try
        {
            string identifier = MarshalString(simpleble_peripheral_identifier(peripheral));
            UnityMainThreadDispatcher.Enqueue(() => OnDeviceDisconnected?.Invoke(identifier));
        }
        catch (Exception e)
        {
            Debug.LogError($"Error in device disconnected callback: {e.Message}");
        }
    }

    [AOT.MonoPInvokeCallback(typeof(NotificationCallback))]
    private static void OnNotificationReceived(IntPtr peripheral, SimpleBLEUUID service, SimpleBLEUUID characteristic, IntPtr data, UIntPtr data_length, IntPtr userdata)
    {
        try
        {
            string deviceId = MarshalString(simpleble_peripheral_identifier(peripheral));
            byte[] notificationData = new byte[data_length.ToUInt32()];
            Marshal.Copy(data, notificationData, 0, (int)data_length.ToUInt32());

            UnityMainThreadDispatcher.Enqueue(() =>
                OnCharacteristicValueChanged?.Invoke(deviceId, service.value, characteristic.value, notificationData));
        }
        catch (Exception e)
        {
            Debug.LogError($"Error in notification callback: {e.Message}");
        }
    }

#endif

    #endregion

    #region Android Implementation

#if UNITY_ANDROID && !UNITY_EDITOR

    // Android implementation using SimpleBLE .so library
    // Note: This is a placeholder implementation
    // The actual Android implementation would require JNI bindings or a custom Android plugin

    private static bool AndroidInitialize()
    {
        OnError?.Invoke("Android SimpleBLE implementation not yet available");
        return false;
    }

    private static bool AndroidStartScan()
    {
        OnError?.Invoke("Android SimpleBLE implementation not yet available");
        return false;
    }

    private static bool AndroidStopScan()
    {
        OnError?.Invoke("Android SimpleBLE implementation not yet available");
        return false;
    }

    private static bool AndroidConnectDevice(string deviceId)
    {
        OnError?.Invoke("Android SimpleBLE implementation not yet available");
        return false;
    }

    private static bool AndroidDisconnectDevice(string deviceId)
    {
        OnError?.Invoke("Android SimpleBLE implementation not yet available");
        return false;
    }

    private static byte[] AndroidReadCharacteristic(string deviceId, string serviceUuid, string characteristicUuid)
    {
        OnError?.Invoke("Android SimpleBLE implementation not yet available");
        return null;
    }

    private static bool AndroidWriteCharacteristic(string deviceId, string serviceUuid, string characteristicUuid, byte[] data, bool withResponse)
    {
        OnError?.Invoke("Android SimpleBLE implementation not yet available");
        return false;
    }

    private static bool AndroidSubscribeCharacteristic(string deviceId, string serviceUuid, string characteristicUuid)
    {
        OnError?.Invoke("Android SimpleBLE implementation not yet available");
        return false;
    }

    private static bool AndroidUnsubscribeCharacteristic(string deviceId, string serviceUuid, string characteristicUuid)
    {
        OnError?.Invoke("Android SimpleBLE implementation not yet available");
        return false;
    }

    private static List<BLEDeviceInfo> AndroidGetDiscoveredDevices()
    {
        return new List<BLEDeviceInfo>();
    }

    private static void AndroidCleanup()
    {
        // Android cleanup implementation
    }

#endif

    #endregion

    #region Common Implementation

    private static bool _initialized = false;
    
    // 确保在主线程执行操作
    private static class UnityMainThreadDispatcher
    {
        private static readonly Queue<Action> _executionQueue = new Queue<Action>();

        public static void Enqueue(Action action)
        {
            lock (_executionQueue)
            {
                _executionQueue.Enqueue(action);
            }
        }

        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
        private static void Init()
        {
            GameObject dispatcher = new GameObject("MainThreadDispatcher");
            dispatcher.AddComponent<MainThreadDispatcherBehaviour>();
            UnityEngine.Object.DontDestroyOnLoad(dispatcher);
        }

        private class MainThreadDispatcherBehaviour : MonoBehaviour
        {
            private void Update()
            {
                lock (_executionQueue)
                {
                    while (_executionQueue.Count > 0)
                    {
                        _executionQueue.Dequeue().Invoke();
                    }
                }
            }
        }
    }

    #endregion
}