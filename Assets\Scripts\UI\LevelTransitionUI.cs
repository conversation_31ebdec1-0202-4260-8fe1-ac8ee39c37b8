using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Events;
using ForceFollowingGame.Core;
using ForceFollowingGame.Game;
using ForceFollowingGame.Data;

namespace ForceFollowingGame.UI
{
    /// <summary>
    /// 关卡间休息界面 - 显示关卡完成结果和下一关预览
    /// </summary>
    public class LevelTransitionUI : MonoBehaviour
    {
        public static LevelTransitionUI Instance { get; private set; }

        [Header("结果显示")]
        [SerializeField] private Text levelCompleteText;           // "第X关完成!"
        [SerializeField] private Text finalScoreText;              // "最终得分: XXX"
        [SerializeField] private Text accuracyText;                // "跟随精度: XX%"
        [SerializeField] private Text consistencyText;             // "稳定性: XX%"
        [SerializeField] private Image[] starsDisplay;             // 星级显示

        [Header("下一关预览")]
        [SerializeField] private Text nextLevelNameText;           // 下一关名称
        [SerializeField] private Text nextLevelDescText;           // 下一关描述
        [SerializeField] private Image nextLevelPreview;           // 下一关轨迹预览

        [Header("操作按钮")]
        [SerializeField] private Button continueButton;            // "继续下一关"
        [SerializeField] private Button restButton;                // "休息一会"
        [SerializeField] private Button quitButton;                // "结束游戏"

        [Header("鼓励文字")]
        [SerializeField] private Text encouragementText;           // 鼓励性文字
        [SerializeField] private string[] perfectMessages = {"完美!", "太棒了!", "你是握力大师!"};
        [SerializeField] private string[] goodMessages = {"很好!", "继续保持!", "不错的表现!"};
        [SerializeField] private string[] okMessages = {"还可以!", "再接再厉!", "你在进步!"};
        [SerializeField] private string[] poorMessages = {"加油!", "别放弃!", "下次会更好!"};

        [Header("动画设置")]
        [SerializeField] private float scoreAnimationDuration = 1.5f;
        [SerializeField] private float percentageAnimationDuration = 1f;
        [SerializeField] private float starAnimationDelay = 0.3f;

        [Header("音效")]
        [SerializeField] private AudioSource audioSource;
        [SerializeField] private AudioClip starSound;
        [SerializeField] private AudioClip completionSound;

        [Header("事件")]
        public UnityEvent OnContinueToNextLevel;
        public UnityEvent OnRestRequested;
        public UnityEvent OnQuitRequested;

        void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
            }
            else
            {
                Destroy(gameObject);
            }
        }

        void Start()
        {
            InitializeButtons();
        }

        /// <summary>
        /// 初始化按钮事件
        /// </summary>
        private void InitializeButtons()
        {
            if (continueButton != null)
                continueButton.onClick.AddListener(StartNextLevel);

            if (restButton != null)
                restButton.onClick.AddListener(ShowRestOptions);

            if (quitButton != null)
                quitButton.onClick.AddListener(QuitGame);
        }

        /// <summary>
        /// 显示结果
        /// </summary>
        /// <param name="performance">性能指标</param>
        public void ShowResults(PerformanceMetrics performance)
        {
            gameObject.SetActive(true);
            StartCoroutine(AnimateResults(performance));
            PreviewNextLevel();
        }

        /// <summary>
        /// 动画显示结果
        /// </summary>
        private IEnumerator AnimateResults(PerformanceMetrics performance)
        {
            // 播放完成音效
            if (audioSource != null && completionSound != null)
                audioSource.PlayOneShot(completionSound);

            // 显示关卡完成文字
            if (levelCompleteText != null && LevelManager.Instance != null)
            {
                int levelIndex = LevelManager.Instance.GetCurrentLevelIndex();
                levelCompleteText.text = $"第{levelIndex + 1}关完成!";
            }

            yield return new WaitForSeconds(0.5f);

            // 分数动画
            yield return StartCoroutine(AnimateScore(0, performance.finalScore));

            // 精度动画
            yield return StartCoroutine(AnimatePercentage(accuracyText, "跟随精度", performance.accuracy));

            // 稳定性动画
            yield return StartCoroutine(AnimatePercentage(consistencyText, "稳定性", performance.consistency));

            // 星级动画
            yield return StartCoroutine(AnimateStars(performance.stars));

            // 鼓励文字
            ShowEncouragement(performance.stars);

            // 启用按钮
            EnableButtons();
        }

        /// <summary>
        /// 分数动画
        /// </summary>
        private IEnumerator AnimateScore(float from, float to)
        {
            if (finalScoreText == null) yield break;

            float elapsed = 0f;

            while (elapsed < scoreAnimationDuration)
            {
                elapsed += Time.deltaTime;
                float currentScore = Mathf.Lerp(from, to, elapsed / scoreAnimationDuration);
                finalScoreText.text = $"最终得分: {currentScore:F0}";
                yield return null;
            }

            finalScoreText.text = $"最终得分: {to:F0}";
        }

        /// <summary>
        /// 百分比动画
        /// </summary>
        private IEnumerator AnimatePercentage(Text targetText, string label, float percentage)
        {
            if (targetText == null) yield break;

            float elapsed = 0f;

            while (elapsed < percentageAnimationDuration)
            {
                elapsed += Time.deltaTime;
                float current = Mathf.Lerp(0, percentage, elapsed / percentageAnimationDuration);
                targetText.text = $"{label}: {current:F1}%";
                yield return null;
            }

            targetText.text = $"{label}: {percentage:F1}%";
        }

        /// <summary>
        /// 星级动画
        /// </summary>
        private IEnumerator AnimateStars(int starCount)
        {
            if (starsDisplay == null) yield break;

            // 先隐藏所有星星
            foreach (var star in starsDisplay)
            {
                if (star != null)
                {
                    star.color = Color.gray;
                    star.transform.localScale = Vector3.zero;
                }
            }

            // 逐个显示获得的星星
            for (int i = 0; i < starCount && i < starsDisplay.Length; i++)
            {
                yield return new WaitForSeconds(starAnimationDelay);

                var star = starsDisplay[i];
                if (star != null)
                {
                    star.color = Color.yellow;

                    // 星星出现动画（使用简单的缩放动画）
                    StartCoroutine(ScaleAnimation(star.transform, Vector3.zero, Vector3.one, 0.5f));

                    // 播放星星音效
                    if (audioSource != null && starSound != null)
                        audioSource.PlayOneShot(starSound);
                }
            }
        }

        /// <summary>
        /// 缩放动画
        /// </summary>
        private IEnumerator ScaleAnimation(Transform target, Vector3 from, Vector3 to, float duration)
        {
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;
                
                // 使用弹性缓动效果
                t = 1f - Mathf.Pow(1f - t, 3f);
                
                target.localScale = Vector3.Lerp(from, to, t);
                yield return null;
            }
            
            target.localScale = to;
        }

        /// <summary>
        /// 显示鼓励文字
        /// </summary>
        private void ShowEncouragement(int stars)
        {
            if (encouragementText == null) return;

            string[] messages;
            switch (stars)
            {
                case 3: messages = perfectMessages; break;
                case 2: messages = goodMessages; break;
                case 1: messages = okMessages; break;
                default: messages = poorMessages; break;
            }

            if (messages.Length > 0)
            {
                encouragementText.text = messages[Random.Range(0, messages.Length)];
            }
        }

        /// <summary>
        /// 预览下一关
        /// </summary>
        private void PreviewNextLevel()
        {
            if (LevelManager.Instance == null) return;

            var nextLevel = LevelManager.Instance.PeekNextLevel();
            if (nextLevel != null)
            {
                if (nextLevelNameText != null)
                    nextLevelNameText.text = nextLevel.levelName;

                if (nextLevelDescText != null)
                    nextLevelDescText.text = nextLevel.description;

                // 生成下一关轨迹预览图
                GenerateLevelPreview(nextLevel);
            }
            else
            {
                // 所有关卡完成
                if (nextLevelNameText != null)
                    nextLevelNameText.text = "恭喜完成所有关卡!";

                if (nextLevelDescText != null)
                    nextLevelDescText.text = "你已经是真正的握力大师了!";

                if (continueButton != null)
                    continueButton.gameObject.SetActive(false);
            }
        }

        /// <summary>
        /// 生成关卡预览
        /// </summary>
        private void GenerateLevelPreview(GameLevel level)
        {
            // 这里可以实现轨迹预览图的生成
            // 暂时使用关卡颜色作为预览
            if (nextLevelPreview != null)
            {
                nextLevelPreview.color = level.levelColor;
            }
        }

        /// <summary>
        /// 启用按钮
        /// </summary>
        private void EnableButtons()
        {
            if (continueButton != null)
                continueButton.interactable = true;

            if (restButton != null)
                restButton.interactable = true;

            if (quitButton != null)
                quitButton.interactable = true;
        }

        /// <summary>
        /// 开始下一关
        /// </summary>
        private void StartNextLevel()
        {
            if (LevelManager.Instance != null)
            {
                LevelManager.Instance.LoadNextLevel();
            }

            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.TransitionToState(GameStateManager.GameState.GameLevels);
            }

            OnContinueToNextLevel?.Invoke();
            gameObject.SetActive(false);
        }

        /// <summary>
        /// 显示休息选项
        /// </summary>
        private void ShowRestOptions()
        {
            // 这里可以显示休息选项面板
            OnRestRequested?.Invoke();
            
            Debug.Log("[LevelTransitionUI] 休息选项请求");
        }

        /// <summary>
        /// 退出游戏
        /// </summary>
        private void QuitGame()
        {
            // 保存数据并退出到主菜单
            if (DataManager.Instance != null)
            {
                DataManager.Instance.SaveCurrentSession();
            }

            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.TransitionToState(GameStateManager.GameState.Results);
            }

            OnQuitRequested?.Invoke();
            gameObject.SetActive(false);
        }

        /// <summary>
        /// 隐藏界面
        /// </summary>
        public void Hide()
        {
            gameObject.SetActive(false);
        }

        // 调试方法
        [ContextMenu("Test Show Results")]
        public void TestShowResults()
        {
            var testMetrics = new PerformanceMetrics(85f, 75f, 250f, 850f, 2, 30f);
            ShowResults(testMetrics);
        }
    }
}
