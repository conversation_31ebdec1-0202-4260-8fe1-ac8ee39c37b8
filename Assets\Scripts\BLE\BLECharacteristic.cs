/// <summary>
/// BLE 特征值模型
/// </summary>
public class BLECharacteristic
{
    public string UUID { get; set; }
    public bool CanRead { get; set; }
    public bool CanWrite { get; set; }
    public bool CanNotify { get; set; }
    public byte[] Value { get; set; }

    public BLECharacteristic(SimpleBLE.BLECharacteristicInfo info)
    {
        UUID = info.UUID;
        CanRead = info.CanRead;
        CanWrite = info.CanWrite;
        CanNotify = info.CanNotify;
    }
}