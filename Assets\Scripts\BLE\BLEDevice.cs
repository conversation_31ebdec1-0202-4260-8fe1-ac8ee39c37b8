using System;
using System.Collections.Generic;

/// <summary>
/// BLE 设备模型
/// </summary>
public class BLEDevice
{
    public string Identifier { get; set; }
    public string Address { get; set; }
    public string Name { get; set; }
    public int RSSI { get; set; }
    public bool IsConnected { get; set; }
    public List<BLEService> Services { get; set; } = new List<BLEService>();

    public BLEDevice(SimpleBLE.BLEDeviceInfo info)
    {
        Identifier = info.Identifier;
        Address = info.Address;
        Name = string.IsNullOrEmpty(info.Name) ? "Unknown Device" : info.Name;
        RSSI = info.RSSI;
        IsConnected = false;
    }

    public void UpdateServices(List<SimpleBLE.BLEServiceInfo> servicesInfo)
    {
        Services.Clear();
        foreach (var serviceInfo in servicesInfo)
        {
            Services.Add(new BLEService(serviceInfo));
        }
    }
}