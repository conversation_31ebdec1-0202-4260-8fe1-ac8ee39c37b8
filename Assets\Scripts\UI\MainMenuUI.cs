using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Events;
using ForceFollowingGame.Core;
using ForceFollowingGame.Data;

namespace ForceFollowingGame.UI
{
    /// <summary>
    /// 主菜单UI - 游戏的主入口界面
    /// </summary>
    public class MainMenuUI : MonoBehaviour
    {
        [Header("UI元素")]
        [SerializeField] private Text titleText;                   // 游戏标题
        [SerializeField] private InputField participantIdInput;    // 参与者ID输入
        [SerializeField] private Button startGameButton;           // 开始游戏按钮
        [SerializeField] private Button settingsButton;            // 设置按钮
        [SerializeField] private Button exitButton;                // 退出按钮

        [Header("状态显示")]
        [SerializeField] private Text deviceStatusText;            // 设备状态显示
        [SerializeField] private Text versionText;                 // 版本信息
        [SerializeField] private Image deviceStatusIndicator;      // 设备状态指示器

        [Header("颜色配置")]
        [SerializeField] private Color connectedColor = Color.green;
        [SerializeField] private Color disconnectedColor = Color.red;
        [SerializeField] private Color connectingColor = Color.yellow;

        [Header("事件")]
        public UnityEvent<string> OnStartGameRequested;
        public UnityEvent OnSettingsRequested;
        public UnityEvent OnExitRequested;

        private bool isDeviceConnected = false;

        void Start()
        {
            InitializeUI();
            SetupEventListeners();
        }

        void Update()
        {
            UpdateDeviceStatus();
        }

        /// <summary>
        /// 初始化UI
        /// </summary>
        private void InitializeUI()
        {
            if (titleText != null)
                titleText.text = "力量跟随游戏";

            if (versionText != null)
                versionText.text = $"版本 {Application.version}";

            if (participantIdInput != null)
            {
                participantIdInput.text = GenerateDefaultParticipantId();
                participantIdInput.onValueChanged.AddListener(OnParticipantIdChanged);
            }

            UpdateStartButtonState();
        }

        /// <summary>
        /// 设置事件监听器
        /// </summary>
        private void SetupEventListeners()
        {
            if (startGameButton != null)
                startGameButton.onClick.AddListener(OnStartGameClicked);

            if (settingsButton != null)
                settingsButton.onClick.AddListener(OnSettingsClicked);

            if (exitButton != null)
                exitButton.onClick.AddListener(OnExitClicked);

            // 订阅BLE设备事件
            if (BLEDeviceManager.Instance != null)
            {
                BLEDeviceManager.Instance.OnDeviceConnected.AddListener(OnDeviceConnected);
                BLEDeviceManager.Instance.OnDeviceDisconnected.AddListener(OnDeviceDisconnected);
            }
        }

        /// <summary>
        /// 生成默认参与者ID
        /// </summary>
        private string GenerateDefaultParticipantId()
        {
            return $"P{System.DateTime.Now:yyyyMMdd_HHmm}";
        }

        /// <summary>
        /// 参与者ID改变事件
        /// </summary>
        private void OnParticipantIdChanged(string newId)
        {
            UpdateStartButtonState();
        }

        /// <summary>
        /// 更新开始按钮状态
        /// </summary>
        private void UpdateStartButtonState()
        {
            if (startGameButton == null || participantIdInput == null) return;

            bool canStart = !string.IsNullOrEmpty(participantIdInput.text.Trim());
            startGameButton.interactable = canStart;

            // 更新按钮文字
            Text buttonText = startGameButton.GetComponentInChildren<Text>();
            if (buttonText != null)
            {
                if (canStart)
                    buttonText.text = "开始游戏";
                else
                    buttonText.text = "请输入参与者ID";
            }
        }

        /// <summary>
        /// 更新设备状态
        /// </summary>
        private void UpdateDeviceStatus()
        {
            if (BLEDeviceManager.Instance == null) return;

            bool currentConnectionState = BLEDeviceManager.Instance.IsConnected();
            
            if (currentConnectionState != isDeviceConnected)
            {
                isDeviceConnected = currentConnectionState;
                UpdateDeviceStatusDisplay();
            }
        }

        /// <summary>
        /// 更新设备状态显示
        /// </summary>
        private void UpdateDeviceStatusDisplay()
        {
            if (deviceStatusText != null)
            {
                if (isDeviceConnected)
                    deviceStatusText.text = "设备已连接";
                else
                    deviceStatusText.text = "设备未连接";
            }

            if (deviceStatusIndicator != null)
            {
                deviceStatusIndicator.color = isDeviceConnected ? connectedColor : disconnectedColor;
            }
        }

        /// <summary>
        /// 开始游戏按钮点击
        /// </summary>
        private void OnStartGameClicked()
        {
            if (participantIdInput == null) return;

            string participantId = participantIdInput.text.Trim();
            if (string.IsNullOrEmpty(participantId))
            {
                Debug.LogWarning("[MainMenuUI] 参与者ID不能为空");
                return;
            }

            // 开始新会话
            if (DataManager.Instance != null)
            {
                DataManager.Instance.StartNewSession(participantId);
            }

            OnStartGameRequested?.Invoke(participantId);

            // 转换到设备连接状态
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.TransitionToState(GameStateManager.GameState.DeviceConnection);
            }

            Debug.Log($"[MainMenuUI] 开始游戏，参与者ID: {participantId}");
        }

        /// <summary>
        /// 设置按钮点击
        /// </summary>
        private void OnSettingsClicked()
        {
            OnSettingsRequested?.Invoke();

            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.TransitionToState(GameStateManager.GameState.Settings);
            }

            Debug.Log("[MainMenuUI] 打开设置");
        }

        /// <summary>
        /// 退出按钮点击
        /// </summary>
        private void OnExitClicked()
        {
            OnExitRequested?.Invoke();

            // 保存当前数据
            if (DataManager.Instance != null)
            {
                DataManager.Instance.SaveCurrentSession();
            }

            Debug.Log("[MainMenuUI] 退出游戏");

#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }

        /// <summary>
        /// 设备连接事件
        /// </summary>
        private void OnDeviceConnected()
        {
            isDeviceConnected = true;
            UpdateDeviceStatusDisplay();
            
            Debug.Log("[MainMenuUI] 设备连接成功");
        }

        /// <summary>
        /// 设备断开事件
        /// </summary>
        private void OnDeviceDisconnected()
        {
            isDeviceConnected = false;
            UpdateDeviceStatusDisplay();
            
            Debug.Log("[MainMenuUI] 设备连接断开");
        }

        /// <summary>
        /// 显示连接状态
        /// </summary>
        public void ShowConnectingStatus()
        {
            if (deviceStatusText != null)
                deviceStatusText.text = "正在连接设备...";

            if (deviceStatusIndicator != null)
                deviceStatusIndicator.color = connectingColor;
        }

        /// <summary>
        /// 显示连接错误
        /// </summary>
        public void ShowConnectionError(string error)
        {
            if (deviceStatusText != null)
                deviceStatusText.text = $"连接失败: {error}";

            if (deviceStatusIndicator != null)
                deviceStatusIndicator.color = disconnectedColor;
        }

        /// <summary>
        /// 设置参与者ID
        /// </summary>
        public void SetParticipantId(string id)
        {
            if (participantIdInput != null)
            {
                participantIdInput.text = id;
                UpdateStartButtonState();
            }
        }

        /// <summary>
        /// 获取参与者ID
        /// </summary>
        public string GetParticipantId()
        {
            return participantIdInput != null ? participantIdInput.text.Trim() : "";
        }

        /// <summary>
        /// 显示主菜单
        /// </summary>
        public void Show()
        {
            gameObject.SetActive(true);
        }

        /// <summary>
        /// 隐藏主菜单
        /// </summary>
        public void Hide()
        {
            gameObject.SetActive(false);
        }

        void OnDestroy()
        {
            // 取消事件订阅
            if (BLEDeviceManager.Instance != null)
            {
                BLEDeviceManager.Instance.OnDeviceConnected.RemoveListener(OnDeviceConnected);
                BLEDeviceManager.Instance.OnDeviceDisconnected.RemoveListener(OnDeviceDisconnected);
            }
        }

        // 调试方法
        [ContextMenu("Test Start Game")]
        public void TestStartGame()
        {
            SetParticipantId("TestUser");
            OnStartGameClicked();
        }

        [ContextMenu("Test Device Connected")]
        public void TestDeviceConnected()
        {
            OnDeviceConnected();
        }

        [ContextMenu("Test Device Disconnected")]
        public void TestDeviceDisconnected()
        {
            OnDeviceDisconnected();
        }
    }
}
