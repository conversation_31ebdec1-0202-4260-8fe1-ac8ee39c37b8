using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Events;
using ForceFollowingGame.Core;
using ForceFollowingGame.Data;

namespace ForceFollowingGame.Calibration
{
    /// <summary>
    /// MVC校准游戏 - 游戏化的最大握力测量
    /// </summary>
    public class MVCCalibrationGame : MonoBehaviour
    {
        [Header("校准参数")]
        [SerializeField] private float calibrationDuration = 15f;    // 校准持续时间
        [SerializeField] private float warmupDuration = 3f;          // 热身时间
        [SerializeField] private float measurementDuration = 10f;    // 实际测量时间
        [SerializeField] private float cooldownDuration = 2f;        // 冷却时间

        [Header("UI元素")]
        [SerializeField] private Text instructionText;               // 指导文字
        [SerializeField] private Text countdownText;                 // 倒计时
        [SerializeField] private Slider forceProgressBar;            // 力量进度条
        [SerializeField] private Text currentForceText;              // 当前力量显示
        [SerializeField] private Text maxForceText;                  // 最大力量显示
        [SerializeField] private ParticleSystem encouragementEffect; // 鼓励特效
        [SerializeField] private Button startButton;                 // 开始按钮

        [Header("音效")]
        [SerializeField] private AudioSource audioSource;
        [SerializeField] private AudioClip countdownBeep;
        [SerializeField] private AudioClip startSound;
        [SerializeField] private AudioClip newRecordSound;
        [SerializeField] private AudioClip completionSound;

        [Header("视觉反馈")]
        [SerializeField] private Color normalColor = Color.white;
        [SerializeField] private Color measurementColor = Color.red;
        [SerializeField] private Color completedColor = Color.green;

        private float currentMVC = 0f;
        private float sessionMaxForce = 0f;
        private float calibrationTimer = 0f;
        private CalibrationPhase currentPhase;
        private bool isCalibrationActive = false;

        public enum CalibrationPhase
        {
            Instruction,    // 说明阶段
            Warmup,         // 热身阶段
            Measurement,    // 测量阶段
            Cooldown,       // 冷却阶段
            Complete        // 完成
        }

        [Header("事件")]
        public UnityEvent<float> OnMVCCalibrated;
        public UnityEvent OnCalibrationStarted;
        public UnityEvent OnCalibrationCompleted;

        void Start()
        {
            InitializeCalibration();
        }

        void Update()
        {
            if (isCalibrationActive && currentPhase != CalibrationPhase.Complete)
            {
                calibrationTimer += Time.deltaTime;
                UpdateCalibrationPhase();
                UpdateUI();
            }
        }

        void OnEnable()
        {
            // 订阅BLE数据事件
            if (BLEDeviceManager.Instance != null)
            {
                BLEDeviceManager.Instance.OnForceDataReceived.AddListener(OnForceDataReceived);
            }
        }

        void OnDisable()
        {
            // 取消订阅BLE数据事件
            if (BLEDeviceManager.Instance != null)
            {
                BLEDeviceManager.Instance.OnForceDataReceived.RemoveListener(OnForceDataReceived);
            }
        }

        /// <summary>
        /// 初始化校准系统
        /// </summary>
        private void InitializeCalibration()
        {
            currentPhase = CalibrationPhase.Instruction;
            isCalibrationActive = false;
            calibrationTimer = 0f;
            sessionMaxForce = 0f;

            if (startButton != null)
            {
                startButton.onClick.AddListener(StartCalibration);
            }

            ShowInstructions();
        }

        /// <summary>
        /// 显示说明
        /// </summary>
        private void ShowInstructions()
        {
            if (instructionText != null)
            {
                instructionText.text = "MVC握力测量\n\n" +
                    "接下来将测量你的最大握力\n" +
                    "请在听到提示音后用力握紧\n" +
                    "并保持10秒钟\n\n" +
                    "准备好了吗？";
                instructionText.color = normalColor;
            }

            if (countdownText != null)
                countdownText.text = "";

            if (forceProgressBar != null)
                forceProgressBar.value = 0f;

            if (startButton != null)
                startButton.gameObject.SetActive(true);
        }

        /// <summary>
        /// 开始校准
        /// </summary>
        public void StartCalibration()
        {
            currentPhase = CalibrationPhase.Warmup;
            isCalibrationActive = true;
            calibrationTimer = 0f;
            sessionMaxForce = 0f;

            if (startButton != null)
                startButton.gameObject.SetActive(false);

            OnCalibrationStarted?.Invoke();
            
            Debug.Log("[MVCCalibrationGame] 开始MVC校准");
            
            StartCoroutine(WarmupCountdown());
        }

        /// <summary>
        /// 热身倒计时
        /// </summary>
        private IEnumerator WarmupCountdown()
        {
            if (instructionText != null)
            {
                instructionText.text = "准备开始...";
                instructionText.color = normalColor;
            }

            for (int i = 3; i > 0; i--)
            {
                if (countdownText != null)
                    countdownText.text = i.ToString();
                
                if (audioSource != null && countdownBeep != null)
                    audioSource.PlayOneShot(countdownBeep);
                
                yield return new WaitForSeconds(1f);
            }

            if (countdownText != null)
                countdownText.text = "用力握紧!";
            
            if (audioSource != null && startSound != null)
                audioSource.PlayOneShot(startSound);

            StartMeasurement();
        }

        /// <summary>
        /// 开始测量
        /// </summary>
        private void StartMeasurement()
        {
            currentPhase = CalibrationPhase.Measurement;
            calibrationTimer = 0f;

            if (instructionText != null)
            {
                instructionText.text = "用力握紧并保持!";
                instructionText.color = measurementColor;
            }

            Debug.Log("[MVCCalibrationGame] 开始MVC测量阶段");
        }

        /// <summary>
        /// 更新校准阶段
        /// </summary>
        private void UpdateCalibrationPhase()
        {
            switch (currentPhase)
            {
                case CalibrationPhase.Measurement:
                    if (calibrationTimer >= measurementDuration)
                    {
                        StartCooldown();
                    }
                    break;

                case CalibrationPhase.Cooldown:
                    if (calibrationTimer >= cooldownDuration)
                    {
                        CompleteCalibration();
                    }
                    break;
            }
        }

        /// <summary>
        /// 开始冷却
        /// </summary>
        private void StartCooldown()
        {
            currentPhase = CalibrationPhase.Cooldown;
            calibrationTimer = 0f;

            if (instructionText != null)
            {
                instructionText.text = "很好! 现在请放松...";
                instructionText.color = normalColor;
            }

            if (countdownText != null)
                countdownText.text = "";

            Debug.Log("[MVCCalibrationGame] 开始冷却阶段");
        }

        /// <summary>
        /// 完成校准
        /// </summary>
        private void CompleteCalibration()
        {
            currentPhase = CalibrationPhase.Complete;
            isCalibrationActive = false;
            currentMVC = sessionMaxForce;

            if (instructionText != null)
            {
                instructionText.text = $"MVC测量完成!\n最大握力: {currentMVC:F1}";
                instructionText.color = completedColor;
            }

            if (audioSource != null && completionSound != null)
                audioSource.PlayOneShot(completionSound);

            // 触发完成事件
            OnMVCCalibrated?.Invoke(currentMVC);
            OnCalibrationCompleted?.Invoke();

            // 保存MVC值
            if (DataManager.Instance != null)
            {
                DataManager.Instance.SetMVCValue(currentMVC);
            }

            Debug.Log($"[MVCCalibrationGame] MVC校准完成，最大握力: {currentMVC:F1}");

            // 2秒后转到下一阶段
            StartCoroutine(ProceedToNextPhaseAfterDelay(2f));
        }

        /// <summary>
        /// 延迟后进入下一阶段
        /// </summary>
        private IEnumerator ProceedToNextPhaseAfterDelay(float delay)
        {
            yield return new WaitForSeconds(delay);
            ProceedToNextPhase();
        }

        /// <summary>
        /// 更新UI
        /// </summary>
        private void UpdateUI()
        {
            if (currentPhase == CalibrationPhase.Measurement)
            {
                // 更新倒计时
                float timeRemaining = measurementDuration - calibrationTimer;
                if (countdownText != null)
                    countdownText.text = $"{timeRemaining:F1}s";

                // 更新进度条
                if (forceProgressBar != null)
                    forceProgressBar.value = calibrationTimer / measurementDuration;
            }

            // 更新力量显示
            float currentForce = BLEDeviceManager.Instance != null ? 
                BLEDeviceManager.Instance.GetCurrentForce() : 0f;
            
            if (currentForceText != null)
                currentForceText.text = $"当前: {currentForce:F1}";
            
            if (maxForceText != null)
                maxForceText.text = $"最大: {sessionMaxForce:F1}";
        }

        /// <summary>
        /// 处理力量数据
        /// </summary>
        public void OnForceDataReceived(float forceValue)
        {
            if (currentPhase == CalibrationPhase.Measurement)
            {
                if (forceValue > sessionMaxForce)
                {
                    sessionMaxForce = forceValue;

                    // 新记录特效
                    if (encouragementEffect != null && !encouragementEffect.isPlaying)
                        encouragementEffect.Play();

                    if (audioSource != null && newRecordSound != null)
                        audioSource.PlayOneShot(newRecordSound);

                    Debug.Log($"[MVCCalibrationGame] 新的最大握力记录: {sessionMaxForce:F1}");
                }
            }
        }

        /// <summary>
        /// 进入下一阶段
        /// </summary>
        private void ProceedToNextPhase()
        {
            // 转到REST校准
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.TransitionToState(GameStateManager.GameState.RESTCalibration);
            }
        }

        /// <summary>
        /// 获取当前MVC值
        /// </summary>
        public float GetCurrentMVC()
        {
            return currentMVC;
        }

        /// <summary>
        /// 获取当前校准阶段
        /// </summary>
        public CalibrationPhase GetCurrentPhase()
        {
            return currentPhase;
        }

        /// <summary>
        /// 检查校准是否完成
        /// </summary>
        public bool IsCalibrationComplete()
        {
            return currentPhase == CalibrationPhase.Complete;
        }

        /// <summary>
        /// 重置校准
        /// </summary>
        public void ResetCalibration()
        {
            isCalibrationActive = false;
            currentPhase = CalibrationPhase.Instruction;
            calibrationTimer = 0f;
            sessionMaxForce = 0f;
            currentMVC = 0f;

            ShowInstructions();

            Debug.Log("[MVCCalibrationGame] 校准已重置");
        }

        /// <summary>
        /// 强制完成校准（调试用）
        /// </summary>
        [ContextMenu("Force Complete Calibration")]
        public void ForceCompleteCalibration()
        {
            sessionMaxForce = 50f; // 模拟值
            CompleteCalibration();
        }

        /// <summary>
        /// 设置模拟MVC值（测试用）
        /// </summary>
        public void SetSimulatedMVC(float mvcValue)
        {
            sessionMaxForce = mvcValue;
            currentMVC = mvcValue;
            
            Debug.Log($"[MVCCalibrationGame] 设置模拟MVC值: {mvcValue}");
        }
    }
}
