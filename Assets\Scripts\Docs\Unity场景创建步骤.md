# Unity场景创建详细步骤

## 第一步：创建基础场景结构

### 1. 创建新场景
1. 在Unity中，File → New Scene → Basic (Built-in)
2. File → Save As → 保存为 `Assets/Scenes/MainScene.unity`
3. 创建 `Assets/Scenes/` 文件夹（如果不存在）

### 2. 删除默认对象
- 删除默认的 `Main Camera`（我们会重新创建）
- 保留 `Directional Light`

## 第二步：创建GameManager

### 1. 创建GameManager GameObject
1. 右键 Hierarchy → Create Empty
2. 重命名为 "GameManager"
3. 添加 `GameManager.cs` 脚本组件
4. 设置 Position: (0, 0, 0)

### 2. 配置GameManager组件
- Auto Start In Editor: ✓
- Debug Participant Id: "DebugUser"
- 其他引用稍后设置

## 第三步：创建Canvas UI系统

### 1. 创建Canvas
1. 右键 Hierarchy → UI → Canvas
2. 重命名为 "Canvas"
3. 配置Canvas组件：
   - Render Mode: Screen Space - Overlay
   - Pixel Perfect: ✓

### 2. 配置Canvas Scaler
- UI Scale Mode: Scale With Screen Size
- Reference Resolution: 1920 x 1080
- Screen Match Mode: Match Width Or Height
- Match: 0.5

### 3. 添加GraphicRaycaster组件（应该已自动添加）

## 第四步：创建UI面板

### 1. MainMenuUI
1. 右键 Canvas → Create Empty
2. 重命名为 "MainMenuUI"
3. 添加 `MainMenuUI.cs` 脚本
4. 添加子对象：

#### Title Text
- 右键 MainMenuUI → UI → Text
- 重命名为 "TitleText"
- Text: "力量跟随游戏"
- Font Size: 48
- Alignment: Center
- Color: White
- Anchor: Top Center
- Position: (0, -100, 0)

#### Participant ID Input
- 右键 MainMenuUI → UI → Input Field
- 重命名为 "ParticipantIdInput"
- Placeholder Text: "请输入参与者ID"
- Anchor: Center
- Position: (0, 0, 0)
- Width: 400, Height: 50

#### Start Game Button
- 右键 MainMenuUI → UI → Button
- 重命名为 "StartGameButton"
- Button Text: "开始游戏"
- Anchor: Center
- Position: (0, -80, 0)
- Width: 200, Height: 60

#### Settings Button
- 右键 MainMenuUI → UI → Button
- 重命名为 "SettingsButton"
- Button Text: "设置"
- Anchor: Center
- Position: (-120, -160, 0)
- Width: 100, Height: 40

#### Exit Button
- 右键 MainMenuUI → UI → Button
- 重命名为 "ExitButton"
- Button Text: "退出"
- Anchor: Center
- Position: (120, -160, 0)
- Width: 100, Height: 40

#### Device Status Text
- 右键 MainMenuUI → UI → Text
- 重命名为 "DeviceStatusText"
- Text: "设备未连接"
- Anchor: Bottom Left
- Position: (20, 60, 0)

#### Device Status Indicator
- 右键 MainMenuUI → UI → Image
- 重命名为 "DeviceStatusIndicator"
- Color: Red
- Anchor: Bottom Left
- Position: (20, 20, 0)
- Width: 20, Height: 20

#### Version Text
- 右键 MainMenuUI → UI → Text
- 重命名为 "VersionText"
- Text: "版本 1.0.0"
- Anchor: Bottom Right
- Position: (-20, 20, 0)

### 2. DeviceConnectionUI
1. 右键 Canvas → Create Empty
2. 重命名为 "DeviceConnectionUI"
3. 添加 `DeviceConnectionUI.cs` 脚本
4. 设置为非激活状态
5. 添加子对象：

#### Status Text
- UI → Text, 重命名为 "StatusText"
- Text: "准备连接设备..."
- Font Size: 24
- Anchor: Center
- Position: (0, 100, 0)

#### Device Info Text
- UI → Text, 重命名为 "DeviceInfoText"
- Text: "目标设备: 3C:AB:72:6F:68:6D"
- Anchor: Center
- Position: (0, 50, 0)

#### Connect Button
- UI → Button, 重命名为 "ConnectButton"
- Text: "连接设备"
- Anchor: Center
- Position: (0, -50, 0)

#### Connection Progress
- UI → Slider, 重命名为 "ConnectionProgress"
- Min Value: 0, Max Value: 1
- Anchor: Center
- Position: (0, 0, 0)
- Width: 400

#### Skip Button (调试用)
- UI → Button, 重命名为 "SkipButton"
- Text: "跳过连接"
- Anchor: Bottom Center
- Position: (0, 50, 0)

### 3. MVCCalibrationUI
1. 右键 Canvas → Create Empty
2. 重命名为 "MVCCalibrationUI"
3. 添加 `MVCCalibrationGame.cs` 脚本
4. 设置为非激活状态
5. 添加必要的UI元素（参考设计文档）

### 4. RESTCalibrationUI
1. 右键 Canvas → Create Empty
2. 重命名为 "RESTCalibrationUI"
3. 添加 `RESTCalibrationGame.cs` 脚本
4. 设置为非激活状态
5. 添加必要的UI元素

### 5. GameplayUI
1. 右键 Canvas → Create Empty
2. 重命名为 "GameplayUI"
3. 添加 `GameplayUI.cs` 脚本
4. 设置为非激活状态
5. 添加游戏UI元素

### 6. LevelTransitionUI
1. 右键 Canvas → Create Empty
2. 重命名为 "LevelTransitionUI"
3. 添加 `LevelTransitionUI.cs` 脚本
4. 设置为非激活状态
5. 添加结果显示元素

## 第五步：创建SystemManagers

### 1. 创建SystemManagers文件夹
1. 右键 Hierarchy → Create Empty
2. 重命名为 "SystemManagers"

### 2. 创建各个管理器
在SystemManagers下创建以下GameObject：

#### GameStateManager
- Create Empty → "GameStateManager"
- 添加 `GameStateManager.cs` 脚本

#### BLEDeviceManager
- Create Empty → "BLEDeviceManager"
- 添加 `BLEDeviceManager.cs` 脚本

#### LevelManager
- Create Empty → "LevelManager"
- 添加 `LevelManager.cs` 脚本
- 添加 `AdaptiveDifficultySystem.cs` 脚本

#### PerformanceAnalyzer
- Create Empty → "PerformanceAnalyzer"
- 添加 `PerformanceAnalyzer.cs` 脚本

#### DataManager
- Create Empty → "DataManager"
- 添加 `DataManager.cs` 脚本

## 第六步：创建GameWorld

### 1. 创建GameWorld文件夹
1. 右键 Hierarchy → Create Empty
2. 重命名为 "GameWorld"

### 2. 创建Main Camera
1. 右键 GameWorld → Camera
2. 重命名为 "Main Camera"
3. 配置：
   - Position: (0, 0, -10)
   - Projection: Orthographic
   - Size: 5
   - Background: 深蓝色 (0.1, 0.1, 0.3, 1)

### 3. 创建PlayerBird
1. 右键 GameWorld → Create Empty
2. 重命名为 "PlayerBird"
3. 添加 Sprite Renderer 组件
4. Position: (-8, 0, 0)
5. 可以使用Unity默认的Knob精灵或自定义精灵

### 4. 创建TargetPath
1. 右键 GameWorld → Create Empty
2. 重命名为 "TargetPath"
3. 添加 Line Renderer 组件
4. 配置：
   - Material: Default-Line
   - Color: White
   - Width: 0.1
   - Use World Space: false

### 5. 创建Effects文件夹
1. 右键 GameWorld → Create Empty
2. 重命名为 "Effects"
3. 在其下创建粒子系统：

#### SuccessEffect
- 右键 Effects → Effects → Particle System
- 重命名为 "SuccessEffect"
- 配置绿色粒子效果

#### TrailEffect
- 右键 Effects → Effects → Particle System
- 重命名为 "TrailEffect"
- 配置轨迹跟随效果

## 第七步：创建Audio系统

### 1. 创建Audio文件夹
1. 右键 Hierarchy → Create Empty
2. 重命名为 "Audio"

### 2. 创建AudioManager
1. 右键 Audio → Create Empty
2. 重命名为 "AudioManager"
3. 添加 Audio Source 组件

## 第八步：设置引用关系

### 1. 配置GameManager引用
选中GameManager，在Inspector中设置：
- Main Menu UI: 拖拽MainMenuUI
- Device Connection UI: 拖拽DeviceConnectionUI
- MVC Calibration Game: 拖拽MVCCalibrationUI
- REST Calibration Game: 拖拽RESTCalibrationUI
- Gameplay UI: 拖拽GameplayUI
- Level Transition UI: 拖拽LevelTransitionUI

### 2. 配置各UI面板的引用
为每个UI面板设置其子对象的引用

### 3. 配置GameplayUI的游戏对象引用
- Game Area: Canvas的RectTransform
- Target Path: TargetPath的LineRenderer
- Player Bird: PlayerBird的Transform
- Success Effect: SuccessEffect的ParticleSystem
- Trail Effect: TrailEffect的ParticleSystem

## 第九步：保存和测试

### 1. 保存场景
- Ctrl+S 保存场景
- 确保保存在 Assets/Scenes/MainScene.unity

### 2. 设置为启动场景
- File → Build Settings
- Add Open Scenes
- 确保MainScene在列表顶部

### 3. 测试运行
- 点击Play按钮测试
- 检查Console是否有错误
- 验证UI显示是否正确

## 注意事项

1. **脚本引用**: 确保所有脚本都已正确添加到项目中
2. **UI适配**: 测试不同分辨率下的UI显示
3. **性能优化**: 合理设置粒子系统参数
4. **错误处理**: 检查Console中的错误信息并修复

这个步骤指南提供了完整的场景创建流程，按照这些步骤可以构建出完整的游戏场景结构。
