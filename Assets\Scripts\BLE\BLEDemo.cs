using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// BLE 功能演示
/// </summary>
public class BLEDemo : MonoBehaviour
{
    public Button scanButton;
    public Button stopButton;
    public Button connectButton;
    public Button disconnectButton;
    public Button discoverServicesButton;
    public Button readButton;
    public Button writeButton;
    public Button subscribeButton;
    
    public Text statusText;
    public Text deviceInfoText;
    public Dropdown deviceDropdown;
    
    private SortedDictionary<string, BLEDevice> _devices = new SortedDictionary<string, BLEDevice>();
    private BLEDevice _selectedDevice;
    
    private void Start()
    {
        // 初始化UI
        scanButton.onClick.AddListener(StartScan);
        stopButton.onClick.AddListener(StopScan);
        connectButton.onClick.AddListener(ConnectSelectedDevice);
        disconnectButton.onClick.AddListener(DisconnectSelectedDevice);
        discoverServicesButton.onClick.AddListener(DiscoverServices);
        readButton.onClick.AddListener(ReadCharacteristic);
        writeButton.onClick.AddListener(WriteCharacteristic);
        subscribeButton.onClick.AddListener(SubscribeCharacteristic);
        deviceDropdown.onValueChanged.AddListener(OnDropdownValueChanged);
        
        // 注册BLE事件
        SimpleBLE.OnDeviceDiscovered += OnDeviceDiscovered;
        SimpleBLE.OnDeviceConnected += OnDeviceConnected;
        SimpleBLE.OnDeviceDisconnected += OnDeviceDisconnected;
        SimpleBLE.OnServicesDiscovered += OnServicesDiscovered;
        SimpleBLE.OnCharacteristicValueChanged += OnCharacteristicValueChanged;
        SimpleBLE.OnError += OnError;
        
        UpdateStatus("Ready to initialize BLE...");
    }
    
    private void StartScan()
    {
        UpdateStatus("Starting scan...");
        _devices.Clear();
        deviceDropdown.ClearOptions();
        SimpleBLE.StartScan();
    }
    
    private void StopScan()
    {
        UpdateStatus("Stopping scan...");
        SimpleBLE.StopScan();
    }
    
    private void ConnectSelectedDevice()
    {
        if (_selectedDevice == null)
        {
            UpdateStatus("No device selected!");
            return;
        }
        
        UpdateStatus($"Connecting to {_selectedDevice.Name}...");
        SimpleBLE.ConnectDevice(_selectedDevice.Identifier);
    }
    
    private void DisconnectSelectedDevice()
    {
        if (_selectedDevice == null || !_selectedDevice.IsConnected)
        {
            UpdateStatus("No connected device!");
            return;
        }
        
        UpdateStatus($"Disconnecting from {_selectedDevice.Name}...");
        SimpleBLE.DisconnectDevice(_selectedDevice.Identifier);
    }
    
    private void DiscoverServices()
    {
        if (_selectedDevice == null || !_selectedDevice.IsConnected)
        {
            UpdateStatus("Connect a device first!");
            return;
        }
        
        UpdateStatus($"Discovering services for {_selectedDevice.Name}...");
        SimpleBLE.DiscoverServices(_selectedDevice.Identifier);
    }
    
    private void ReadCharacteristic()
    {
        if (_selectedDevice == null || !_selectedDevice.IsConnected)
        {
            UpdateStatus("Connect a device first!");
            return;
        }
        
        // 在实际应用中，这里应该选择特定的特征值
        // 这里仅作演示
        if (_selectedDevice.Services.Count > 0 && 
            _selectedDevice.Services[0].Characteristics.Count > 0)
        {
            var service = _selectedDevice.Services[0];
            var characteristic = service.Characteristics[0];
            
            if (characteristic.CanRead)
            {
                UpdateStatus($"Reading characteristic: {characteristic.UUID}");
                SimpleBLE.ReadCharacteristic(
                    _selectedDevice.Identifier, 
                    service.UUID, 
                    characteristic.UUID
                );
            }
            else
            {
                UpdateStatus("Characteristic not readable");
            }
        }
        else
        {
            UpdateStatus("No characteristics available");
        }
    }
    
    private void WriteCharacteristic()
    {
        if (_selectedDevice == null || !_selectedDevice.IsConnected)
        {
            UpdateStatus("Connect a device first!");
            return;
        }
        
        // 在实际应用中，这里应该选择特定的特征值
        // 这里仅作演示
        if (_selectedDevice.Services.Count > 0 && 
            _selectedDevice.Services[0].Characteristics.Count > 0)
        {
            var service = _selectedDevice.Services[0];
            var characteristic = service.Characteristics[0];
            
            if (characteristic.CanWrite)
            {
                byte[] data = new byte[] { 0x01, 0x02, 0x03 }; // 示例数据
                UpdateStatus($"Writing to characteristic: {characteristic.UUID}");
                SimpleBLE.WriteCharacteristic(
                    _selectedDevice.Identifier, 
                    service.UUID, 
                    characteristic.UUID,
                    data,
                    true // 要求响应
                );
            }
            else
            {
                UpdateStatus("Characteristic not writable");
            }
        }
        else
        {
            UpdateStatus("No characteristics available");
        }
    }
    
    private void SubscribeCharacteristic()
    {
        if (_selectedDevice == null || !_selectedDevice.IsConnected)
        {
            UpdateStatus("Connect a device first!");
            return;
        }
        
        // 在实际应用中，这里应该选择特定的特征值
        // 这里仅作演示
        if (_selectedDevice.Services.Count > 0 && 
            _selectedDevice.Services[0].Characteristics.Count > 0)
        {
            var service = _selectedDevice.Services[0];
            var characteristic = service.Characteristics[0];
            
            if (characteristic.CanNotify)
            {
                UpdateStatus($"Subscribing to characteristic: {characteristic.UUID}");
                SimpleBLE.SubscribeCharacteristic(
                    _selectedDevice.Identifier, 
                    service.UUID, 
                    characteristic.UUID
                );
            }
            else
            {
                UpdateStatus("Characteristic not notifiable");
            }
        }
        else
        {
            UpdateStatus("No characteristics available");
        }
    }
    
    private void OnDropdownValueChanged(int index)
    {
        _selectedDevice = _devices.Values.ElementAt(index);
    }
    
    #region BLE 事件处理
    
    private void OnDeviceDiscovered(SimpleBLE.BLEDeviceInfo deviceInfo)
    {
        if (!_devices.ContainsKey(deviceInfo.Address))
        {
            BLEDevice device = new BLEDevice(deviceInfo);
            _devices[device.Address] = device;
            
            // 更新UI
            UnityMainThreadDispatcher.Enqueue(() => {
                deviceDropdown.AddOptions(new List<string> { $"{device.Name} {device.Address}" });
                UpdateStatus($"Found device: {device.Identifier} {device.Name} {device.Address} (RSSI: {device.RSSI})");
            });
        }
    }
    
    private void OnDeviceConnected(string deviceId)
    {
        if (_devices.TryGetValue(deviceId, out BLEDevice device))
        {
            device.IsConnected = true;
            _selectedDevice = device;
            
            UnityMainThreadDispatcher.Enqueue(() => {
                UpdateStatus($"Connected to {device.Name}");
                UpdateDeviceInfo(device);
            });
        }
    }
    
    private void OnDeviceDisconnected(string deviceId)
    {
        if (_devices.TryGetValue(deviceId, out BLEDevice device))
        {
            device.IsConnected = false;
            
            UnityMainThreadDispatcher.Enqueue(() => {
                UpdateStatus($"Disconnected from {device.Name}");
                if (_selectedDevice == device)
                {
                    _selectedDevice = null;
                    deviceInfoText.text = "No device selected";
                }
            });
        }
    }
    
    private void OnServicesDiscovered(string deviceId, List<SimpleBLE.BLEServiceInfo> services)
    {
        if (_devices.TryGetValue(deviceId, out BLEDevice device))
        {
            device.UpdateServices(services);
            
            UnityMainThreadDispatcher.Enqueue(() => {
                UpdateStatus($"Discovered {services.Count} services for {device.Name}");
                UpdateDeviceInfo(device);
            });
        }
    }
    
    private void OnCharacteristicValueChanged(string deviceId, string serviceUuid, 
        string characteristicUuid, byte[] value)
    {
        if (_devices.TryGetValue(deviceId, out BLEDevice device))
        {
            UnityMainThreadDispatcher.Enqueue(() => {
                UpdateStatus($"Characteristic changed: {characteristicUuid} - {BitConverter.ToString(value)}");
            });
        }
    }
    
    private void OnError(string message)
    {
        UnityMainThreadDispatcher.Enqueue(() => {
            UpdateStatus($"Error: {message}");
        });
    }
    
    #endregion
    
    #region UI 更新方法
    
    private void UpdateStatus(string message)
    {
        statusText.text = message;
        Debug.Log(message);
    }
    
    private void UpdateDeviceInfo(BLEDevice device)
    {
        string info = $"Device: {device.Name}\n";
        info += $"Address: {device.Address}\n";
        info += $"RSSI: {device.RSSI} dBm\n";
        info += $"Connected: {device.IsConnected}\n\n";
        
        info += $"Services: {device.Services.Count}\n";
        foreach (var service in device.Services)
        {
            info += $"- Service: {service.UUID}\n";
            foreach (var characteristic in service.Characteristics)
            {
                info += $"  - Characteristic: {characteristic.UUID}\n";
                info += $"    Read: {characteristic.CanRead}, Write: {characteristic.CanWrite}, Notify: {characteristic.CanNotify}\n";
            }
        }
        
        deviceInfoText.text = info;
    }
    
    #endregion
    
}

// 主线程调度器（已在SimpleBLE.cs中定义，此处为完整性重复）
public static class UnityMainThreadDispatcher
{
    private static readonly Queue<Action> _executionQueue = new Queue<Action>();

    public static void Enqueue(Action action)
    {
        lock (_executionQueue)
        {
            _executionQueue.Enqueue(action);
        }
    }

    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
    private static void Init()
    {
        GameObject dispatcher = new GameObject("MainThreadDispatcher");
        dispatcher.AddComponent<MainThreadDispatcherBehaviour>();
        UnityEngine.Object.DontDestroyOnLoad(dispatcher);
    }

    private class MainThreadDispatcherBehaviour : MonoBehaviour
    {
        private void Update()
        {
            lock (_executionQueue)
            {
                while (_executionQueue.Count > 0)
                {
                    _executionQueue.Dequeue().Invoke();
                }
            }
        }
    }
}

/// <summary>
/// 数据解析工具：
/// </summary>
public static class BLEDataParser
{
    public static float ParseTemperature(byte[] data)
    {
        // IEEE 11073 浮点格式
        if (data.Length < 2) return 0f;
        int mantissa = (data[1] << 8) | data[0];
        return mantissa * 0.1f;
    }
    
    public static string ParseString(byte[] data)
    {
        return Encoding.UTF8.GetString(data);
    }
    
    public static int ParseInt(byte[] data)
    {
        if (data.Length == 1) return data[0];
        if (data.Length == 2) return (data[1] << 8) | data[0];
        if (data.Length == 4) 
            return (data[3] << 24) | (data[2] << 16) | (data[1] << 8) | data[0];
        return 0;
    }
}