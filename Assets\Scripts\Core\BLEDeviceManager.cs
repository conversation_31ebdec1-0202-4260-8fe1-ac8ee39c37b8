using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Events;

namespace ForceFollowingGame.Core
{
    /// <summary>
    /// BLE设备管理器 - 处理握力计设备的连接和数据接收
    /// 基于Python演示代码的数据解析逻辑
    /// </summary>
    public class BLEDeviceManager : MonoBehaviour
    {
        public static BLEDeviceManager Instance { get; private set; }

        [Header("设备配置")]
        [SerializeField] public string targetDeviceMAC = "3C:AB:72:6F:68:6D";
        [SerializeField] private string notifyCharUUID = "9ECADC24-0EE5-A9E0-93F3-A3B50300406E";

        [Header("连接状态")]
        [SerializeField] private bool isConnected = false;
        [SerializeField] private bool isSimulationMode = false; // Editor模式下使用模拟数据
        
        [Header("数据处理")]
        [SerializeField] private float currentForce = 0f;
        [SerializeField] private float smoothingFactor = 0.8f; // 数据平滑系数
        [SerializeField] private bool enableDataLogging = true;

        [Header("事件")]
        public UnityEvent OnDeviceConnected;
        public UnityEvent OnDeviceDisconnected;
        public UnityEvent<float> OnForceDataReceived;
        public UnityEvent<string> OnConnectionError;

        // 数据解析相关（基于Python代码）
        private Queue<byte> dataBuffer = new Queue<byte>();
        private const int PACKET_SIZE = 6;
        private const byte HEADER_1 = 0x40;
        private const byte HEADER_2 = 0x5C;

        // 模拟数据相关
        private float simulationTime = 0f;
        private float simulationAmplitude = 50f;
        private float simulationFrequency = 0.5f;

        // 数据统计
        private int packetsReceived = 0;
        private int validPackets = 0;
        private float lastDataTime = 0f;

        void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeDeviceManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        void Start()
        {
            // 在Editor模式下自动启用模拟模式
#if UNITY_EDITOR
            isSimulationMode = true;
            Debug.Log("[BLEDeviceManager] Editor模式：启用数据模拟");
#endif
        }

        void Update()
        {
            if (isSimulationMode && isConnected)
            {
                UpdateSimulationData();
            }

            // 检查数据超时
            CheckDataTimeout();
        }

        private void InitializeDeviceManager()
        {
            Debug.Log("[BLEDeviceManager] BLE设备管理器初始化完成");
        }

        /// <summary>
        /// 连接到BLE设备
        /// </summary>
        public async Task<bool> ConnectToDevice()
        {
            try
            {
                Debug.Log($"[BLEDeviceManager] 尝试连接设备: {targetDeviceMAC}");

#if UNITY_ANDROID && !UNITY_EDITOR
                return await ConnectAndroid();
#elif UNITY_STANDALONE_WIN && !UNITY_EDITOR
                return await ConnectWindows();
#else
                return SimulateConnection();
#endif
            }
            catch (Exception e)
            {
                Debug.LogError($"[BLEDeviceManager] 设备连接失败: {e.Message}");
                OnConnectionError?.Invoke(e.Message);
                return false;
            }
        }

        /// <summary>
        /// 断开设备连接
        /// </summary>
        public async Task DisconnectDevice()
        {
            if (!isConnected) return;

            try
            {
#if UNITY_ANDROID && !UNITY_EDITOR
                await DisconnectAndroid();
#elif UNITY_STANDALONE_WIN && !UNITY_EDITOR
                await DisconnectWindows();
#else
                SimulateDisconnection();
#endif
            }
            catch (Exception e)
            {
                Debug.LogError($"[BLEDeviceManager] 设备断开失败: {e.Message}");
            }
        }

        /// <summary>
        /// 模拟连接（Editor和测试用）
        /// </summary>
        public bool SimulateConnection()
        {
            isConnected = true;
            isSimulationMode = true;
            
            Debug.Log("[BLEDeviceManager] 模拟连接成功");
            OnDeviceConnected?.Invoke();
            
            return true;
        }

        /// <summary>
        /// 模拟断开连接
        /// </summary>
        private void SimulateDisconnection()
        {
            isConnected = false;
            isSimulationMode = false;
            
            Debug.Log("[BLEDeviceManager] 模拟断开连接");
            OnDeviceDisconnected?.Invoke();
        }

        /// <summary>
        /// 更新模拟数据
        /// </summary>
        private void UpdateSimulationData()
        {
            simulationTime += Time.deltaTime;
            
            // 生成模拟的握力数据（正弦波 + 噪声）
            float baseForce = Mathf.Sin(simulationTime * simulationFrequency * 2 * Mathf.PI) * simulationAmplitude;
            float noise = UnityEngine.Random.Range(-5f, 5f);
            float simulatedForce = Mathf.Max(0, baseForce + simulationAmplitude + noise);
            
            ProcessForceData(simulatedForce);
        }

        /// <summary>
        /// 处理BLE数据（基于Python代码逻辑）
        /// </summary>
        public void ProcessBLEData(byte[] newData)
        {
            if (newData == null || newData.Length == 0) return;

            packetsReceived++;
            
            // 将新数据添加到缓冲区
            foreach (byte b in newData)
            {
                dataBuffer.Enqueue(b);
            }

            // 解析数据包
            while (dataBuffer.Count >= PACKET_SIZE)
            {
                if (FindAndValidatePacket(out float pressure))
                {
                    validPackets++;
                    ProcessForceData(pressure);
                }
            }
        }

        /// <summary>
        /// 查找并验证数据包（基于Python代码）
        /// </summary>
        private bool FindAndValidatePacket(out float pressure)
        {
            pressure = 0f;

            // 查找数据包头
            while (dataBuffer.Count >= PACKET_SIZE)
            {
                // 寻找包头
                bool headerFound = false;
                while (dataBuffer.Count >= 2)
                {
                    byte first = dataBuffer.Dequeue();
                    if (first == HEADER_1 && dataBuffer.Count > 0 && dataBuffer.Peek() == HEADER_2)
                    {
                        headerFound = true;
                        break;
                    }
                }

                if (!headerFound || dataBuffer.Count < 5)
                {
                    return false;
                }

                // 读取完整数据包
                byte[] packet = new byte[PACKET_SIZE];
                packet[0] = HEADER_1;
                packet[1] = dataBuffer.Dequeue(); // HEADER_2
                
                for (int i = 2; i < PACKET_SIZE; i++)
                {
                    if (dataBuffer.Count > 0)
                        packet[i] = dataBuffer.Dequeue();
                    else
                        return false;
                }

                // 解析24位压力值（基于Python代码）
                uint pressureRaw = ((uint)packet[2] << 16) | 
                                  ((uint)packet[3] << 8) | 
                                  (uint)packet[4];

                // 校验和验证
                byte checksum = (byte)((packet[0] + packet[1] + packet[2] + packet[3] + packet[4]) & 0xFF);
                if (checksum == packet[5])
                {
                    pressure = ConvertRawToPressure(pressureRaw);
                    return true;
                }
                else
                {
                    if (enableDataLogging)
                        Debug.LogWarning($"[BLEDeviceManager] 校验和错误: 期望{packet[5]}, 计算{checksum}");
                }
            }

            return false;
        }

        /// <summary>
        /// 将原始值转换为压力值
        /// </summary>
        private float ConvertRawToPressure(uint rawValue)
        {
            // 将24位原始值转换为标准化压力值
            // 假设最大值16777215对应100%
            return (float)rawValue / 16777215f * 100f;
        }

        /// <summary>
        /// 处理力量数据
        /// </summary>
        private void ProcessForceData(float rawForce)
        {
            // 数据平滑处理
            currentForce = Mathf.Lerp(currentForce, rawForce, 1f - smoothingFactor);
            
            lastDataTime = Time.time;
            
            // 触发数据接收事件
            OnForceDataReceived?.Invoke(currentForce);

            if (enableDataLogging && Time.frameCount % 60 == 0) // 每秒记录一次
            {
                Debug.Log($"[BLEDeviceManager] 当前握力: {currentForce:F2}, 原始值: {rawForce:F2}");
            }
        }

        /// <summary>
        /// 检查数据超时
        /// </summary>
        private void CheckDataTimeout()
        {
            if (isConnected && !isSimulationMode && Time.time - lastDataTime > 5f)
            {
                Debug.LogWarning("[BLEDeviceManager] 数据接收超时，可能设备已断开");
                // 可以在这里触发重连逻辑
            }
        }

        /// <summary>
        /// 获取当前力量值
        /// </summary>
        public float GetCurrentForce()
        {
            return currentForce;
        }

        /// <summary>
        /// 获取连接状态
        /// </summary>
        public bool IsConnected()
        {
            return isConnected;
        }

        /// <summary>
        /// 获取数据统计信息
        /// </summary>
        public string GetDataStats()
        {
            float successRate = packetsReceived > 0 ? (float)validPackets / packetsReceived * 100f : 0f;
            return $"接收包: {packetsReceived}, 有效包: {validPackets}, 成功率: {successRate:F1}%";
        }

        /// <summary>
        /// 设置模拟参数
        /// </summary>
        public void SetSimulationParameters(float amplitude, float frequency)
        {
            simulationAmplitude = amplitude;
            simulationFrequency = frequency;
        }

        // 平台特定的连接方法（需要根据实际BLE插件实现）
#if UNITY_ANDROID && !UNITY_EDITOR
        private async Task<bool> ConnectAndroid()
        {
            // TODO: 实现Android BLE连接
            Debug.Log("[BLEDeviceManager] Android BLE连接 - 待实现");
            return false;
        }

        private async Task DisconnectAndroid()
        {
            // TODO: 实现Android BLE断开
            Debug.Log("[BLEDeviceManager] Android BLE断开 - 待实现");
        }
#endif

#if UNITY_STANDALONE_WIN && !UNITY_EDITOR
        private async Task<bool> ConnectWindows()
        {
            // TODO: 实现Windows BLE连接
            Debug.Log("[BLEDeviceManager] Windows BLE连接 - 待实现");
            return false;
        }

        private async Task DisconnectWindows()
        {
            // TODO: 实现Windows BLE断开
            Debug.Log("[BLEDeviceManager] Windows BLE断开 - 待实现");
        }
#endif

        void OnDestroy()
        {
            if (isConnected)
            {
                _ = DisconnectDevice();
            }
        }
    }
}
