using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using ForceFollowingGame.Core;

namespace ForceFollowingGame.Game
{
    /// <summary>
    /// 自适应难度系统 - 根据玩家表现动态调整游戏难度
    /// </summary>
    public class AdaptiveDifficultySystem : MonoBehaviour
    {
        [Header("性能历史")]
        [SerializeField] private Queue<float> recentScores = new Queue<float>();
        [SerializeField] private int maxHistorySize = 5;

        [Header("难度参数")]
        [SerializeField] private float baseDifficulty = 0.5f;
        [SerializeField] private float difficultyAdjustmentRate = 0.1f;
        [SerializeField] private float minDifficulty = 0.1f;
        [SerializeField] private float maxDifficulty = 1.0f;

        [Header("目标性能")]
        [SerializeField] private float targetPerformance = 0.75f; // 目标75%正确率
        [SerializeField] private float performanceWindow = 0.1f;   // 性能窗口

        [Header("调试")]
        [SerializeField] private bool enableDebugLogs = true;
        [SerializeField] private float currentDifficulty;

        void Start()
        {
            currentDifficulty = baseDifficulty;
            
            if (enableDebugLogs)
                Debug.Log("[AdaptiveDifficultySystem] 自适应难度系统初始化完成");
        }

        /// <summary>
        /// 记录玩家表现
        /// </summary>
        /// <param name="score">表现分数 (0-1)</param>
        public void RecordPerformance(float score)
        {
            score = Mathf.Clamp01(score);
            recentScores.Enqueue(score);
            
            if (recentScores.Count > maxHistorySize)
            {
                recentScores.Dequeue();
            }

            // 更新当前难度
            UpdateDifficulty();

            if (enableDebugLogs)
                Debug.Log($"[AdaptiveDifficultySystem] 记录表现: {score:F2}, 当前难度: {currentDifficulty:F2}");
        }

        /// <summary>
        /// 更新难度
        /// </summary>
        private void UpdateDifficulty()
        {
            float avgPerformance = CalculateAveragePerformance();
            currentDifficulty = AdaptDifficulty(avgPerformance);
        }

        /// <summary>
        /// 生成自适应关卡
        /// </summary>
        /// <param name="levelNumber">关卡编号</param>
        /// <returns>生成的关卡</returns>
        public GameLevel GenerateAdaptiveLevel(int levelNumber)
        {
            float adaptedDifficulty = GetCurrentDifficulty();
            
            var adaptiveLevel = new GameLevel
            {
                levelNumber = levelNumber,
                levelName = $"自适应关卡 {levelNumber - 5}",
                description = GetAdaptiveDescription(adaptedDifficulty),
                baseMVCPercent = CalculateAdaptiveMVC(adaptedDifficulty),
                trajectory = SelectAdaptiveTrajectory(adaptedDifficulty),
                duration = 30f + adaptedDifficulty * 20f,
                difficulty = adaptedDifficulty,
                levelColor = GetAdaptiveColor(adaptedDifficulty)
            };

            // 生成自适应轨迹曲线
            adaptiveLevel.targetCurve = TrajectoryGenerator.GenerateAdaptiveTargetCurve(adaptiveLevel);

            if (enableDebugLogs)
                Debug.Log($"[AdaptiveDifficultySystem] 生成自适应关卡: {adaptiveLevel.levelName}, 难度: {adaptedDifficulty:F2}");

            return adaptiveLevel;
        }

        /// <summary>
        /// 计算平均表现
        /// </summary>
        private float CalculateAveragePerformance()
        {
            if (recentScores.Count == 0) 
                return targetPerformance; // 默认目标水平
            
            return recentScores.Average();
        }

        /// <summary>
        /// 调整难度
        /// </summary>
        private float AdaptDifficulty(float avgPerformance)
        {
            float performanceDiff = avgPerformance - targetPerformance;
            
            // 表现好 -> 增加难度，表现差 -> 降低难度
            float newDifficulty = currentDifficulty + (performanceDiff * difficultyAdjustmentRate);
            
            return Mathf.Clamp(newDifficulty, minDifficulty, maxDifficulty);
        }

        /// <summary>
        /// 获取自适应描述
        /// </summary>
        private string GetAdaptiveDescription(float difficulty)
        {
            if (difficulty < 0.3f) return "轻松模式 - 稳定跟随";
            if (difficulty < 0.6f) return "标准模式 - 适中挑战";
            if (difficulty < 0.8f) return "困难模式 - 复杂轨迹";
            return "地狱模式 - 极限挑战";
        }

        /// <summary>
        /// 计算自适应MVC百分比
        /// </summary>
        private float CalculateAdaptiveMVC(float difficulty)
        {
            // 基础MVC从10%到40%
            float baseMVC = 10f + difficulty * 30f;
            
            // 添加一些随机变化
            float variation = Random.Range(-2f, 2f);
            
            return Mathf.Clamp(baseMVC + variation, 5f, 50f);
        }

        /// <summary>
        /// 选择自适应轨迹类型
        /// </summary>
        private TrajectoryType SelectAdaptiveTrajectory(float difficulty)
        {
            // 根据难度选择轨迹类型
            float rand = Random.Range(0f, 1f);
            
            if (difficulty < 0.2f)
            {
                return TrajectoryType.SteadyHold;
            }
            else if (difficulty < 0.4f)
            {
                return rand < 0.7f ? TrajectoryType.SlowRamp : TrajectoryType.SteadyHold;
            }
            else if (difficulty < 0.6f)
            {
                if (rand < 0.4f) return TrajectoryType.Wave;
                if (rand < 0.8f) return TrajectoryType.SlowRamp;
                return TrajectoryType.SteadyHold;
            }
            else if (difficulty < 0.8f)
            {
                if (rand < 0.3f) return TrajectoryType.Steps;
                if (rand < 0.6f) return TrajectoryType.Wave;
                if (rand < 0.9f) return TrajectoryType.FastRamp;
                return TrajectoryType.SlowRamp;
            }
            else
            {
                if (rand < 0.4f) return TrajectoryType.Random;
                if (rand < 0.7f) return TrajectoryType.Steps;
                return TrajectoryType.Challenge;
            }
        }

        /// <summary>
        /// 获取自适应颜色
        /// </summary>
        private Color GetAdaptiveColor(float difficulty)
        {
            // 从绿色（简单）到红色（困难）的渐变
            return Color.Lerp(Color.green, Color.red, difficulty);
        }

        /// <summary>
        /// 获取当前难度
        /// </summary>
        public float GetCurrentDifficulty()
        {
            return currentDifficulty;
        }

        /// <summary>
        /// 获取平均表现
        /// </summary>
        public float GetAveragePerformance()
        {
            return CalculateAveragePerformance();
        }

        /// <summary>
        /// 重置表现历史
        /// </summary>
        public void ResetPerformanceHistory()
        {
            recentScores.Clear();
            currentDifficulty = baseDifficulty;
            
            if (enableDebugLogs)
                Debug.Log("[AdaptiveDifficultySystem] 表现历史已重置");
        }

        /// <summary>
        /// 获取难度统计信息
        /// </summary>
        public string GetDifficultyStats()
        {
            float avgPerf = GetAveragePerformance();
            return $"当前难度: {currentDifficulty:F2}, " +
                   $"平均表现: {avgPerf:F2}, " +
                   $"历史记录: {recentScores.Count}/{maxHistorySize}";
        }

        /// <summary>
        /// 检查是否需要调整难度
        /// </summary>
        public bool ShouldAdjustDifficulty()
        {
            if (recentScores.Count < 3) return false; // 需要足够的数据
            
            float avgPerformance = CalculateAveragePerformance();
            float deviation = Mathf.Abs(avgPerformance - targetPerformance);
            
            return deviation > performanceWindow;
        }

        /// <summary>
        /// 获取难度建议
        /// </summary>
        public string GetDifficultyRecommendation()
        {
            float avgPerformance = CalculateAveragePerformance();
            
            if (avgPerformance > targetPerformance + performanceWindow)
            {
                return "建议增加难度 - 表现优秀";
            }
            else if (avgPerformance < targetPerformance - performanceWindow)
            {
                return "建议降低难度 - 表现需要改善";
            }
            else
            {
                return "难度适中 - 保持当前设置";
            }
        }

        /// <summary>
        /// 设置目标性能
        /// </summary>
        public void SetTargetPerformance(float target)
        {
            targetPerformance = Mathf.Clamp01(target);
            UpdateDifficulty();
            
            if (enableDebugLogs)
                Debug.Log($"[AdaptiveDifficultySystem] 目标性能设置为: {targetPerformance:F2}");
        }

        /// <summary>
        /// 设置难度调整速率
        /// </summary>
        public void SetAdjustmentRate(float rate)
        {
            difficultyAdjustmentRate = Mathf.Clamp(rate, 0.01f, 0.5f);
            
            if (enableDebugLogs)
                Debug.Log($"[AdaptiveDifficultySystem] 难度调整速率设置为: {difficultyAdjustmentRate:F2}");
        }

        // 调试方法
        [ContextMenu("Print Difficulty Stats")]
        public void DebugPrintStats()
        {
            Debug.Log($"[AdaptiveDifficultySystem] {GetDifficultyStats()}");
            Debug.Log($"[AdaptiveDifficultySystem] {GetDifficultyRecommendation()}");
        }

        [ContextMenu("Reset Difficulty")]
        public void DebugResetDifficulty()
        {
            ResetPerformanceHistory();
        }

        [ContextMenu("Simulate Good Performance")]
        public void DebugSimulateGoodPerformance()
        {
            RecordPerformance(0.9f);
        }

        [ContextMenu("Simulate Poor Performance")]
        public void DebugSimulatePoorPerformance()
        {
            RecordPerformance(0.3f);
        }
    }
}
