# 游戏化力量跟随系统 - 使用说明

## 系统概述

本系统是一个基于Unity开发的游戏化力量跟随实验平台，通过BLE握力计采集数据，让被试者在类似Flappy Bird的游戏环境中完成力量控制任务。

## 主要特性

- **游戏化设计**: 关卡制、评分系统、自适应难度
- **实时数据采集**: 通过BLE握力计获取实时力量数据
- **科学分析**: 详细的性能指标分析和数据导出
- **用户友好**: 直观的视觉反馈和操作界面
- **跨平台支持**: Android平板和Windows PC

## 系统要求

### 硬件要求
- **握力计设备**: BLE握力传感器 (MAC: 3C:AB:72:6F:68:6D)
- **Android设备**: Android 5.0+ (API Level 21+)，支持BLE
- **Windows设备**: Windows 10+，支持蓝牙4.0+

### 软件要求
- Unity 2021.3 LTS 或更高版本
- 对应平台的开发环境

## 安装和设置

### 1. 项目导入
1. 打开Unity Hub
2. 点击"添加项目"，选择项目文件夹
3. 确保Unity版本为2021.3 LTS或更高

### 2. 场景设置
1. 打开 `Assets/Scenes/MainScene.unity`
2. 按照 `Assets/Docs/Unity场景设置指南.md` 配置场景
3. 确保所有脚本组件正确引用

### 3. 构建设置
#### Android构建
1. File → Build Settings → Android
2. 设置包名和版本号
3. Player Settings → Other Settings:
   - Minimum API Level: 21
   - Target API Level: 最新
4. 添加权限:
   - BLUETOOTH
   - BLUETOOTH_ADMIN
   - ACCESS_FINE_LOCATION

#### Windows构建
1. File → Build Settings → PC, Mac & Linux Standalone
2. Architecture: x86_64
3. Scripting Backend: Mono

## 使用流程

### 1. 启动系统
1. 运行游戏应用
2. 系统自动初始化所有组件
3. 显示主菜单界面

### 2. 开始实验
1. **输入参与者信息**
   - 在主菜单输入参与者ID
   - ID格式建议: P20240101_001

2. **设备连接**
   - 点击"开始游戏"
   - 系统自动搜索并连接握力计
   - 等待连接成功提示

3. **MVC校准**
   - 按照屏幕指示进行最大握力测量
   - 听到提示音后用力握紧并保持10秒
   - 系统记录最大握力值

4. **REST校准**
   - 完全放松手部
   - 保持自然状态5秒钟
   - 系统记录静息基线值

5. **游戏关卡**
   - 通过握力控制屏幕上的小鸟
   - 跟随目标轨迹移动
   - 完成5个预定义关卡

6. **自适应关卡**
   - 系统根据表现自动生成关卡
   - 难度动态调整
   - 无限关卡挑战

### 3. 数据收集
- 系统自动记录所有实验数据
- 实时分析性能指标
- 自动保存到本地文件

## 游戏机制

### 控制方式
- **握力输入**: 通过握力计控制小鸟高度
- **目标跟随**: 小鸟需要跟随屏幕上的目标轨迹
- **实时反馈**: 颜色指示器显示跟随精度

### 评分系统
- **精度分数**: 基于与目标轨迹的偏差
- **一致性分数**: 基于力量输出的稳定性
- **反应时间**: 对目标变化的响应速度
- **星级评定**: 0-3星，基于综合表现

### 关卡类型
1. **热身运动**: 稳定保持轻微握力
2. **缓慢爬升**: 跟随缓慢上升轨迹
3. **波浪起伏**: 跟随正弦波轨迹
4. **阶梯挑战**: 快速适应不同握力水平
5. **终极挑战**: 复杂轨迹组合

### 自适应难度
- 根据最近5次表现调整难度
- 目标维持75%的成功率
- 动态生成新的轨迹模式

## 数据分析

### 实时指标
- **当前握力**: 实时显示握力百分比
- **目标偏差**: 与目标轨迹的差异
- **累计得分**: 当前关卡得分
- **完成进度**: 关卡进度百分比

### 关卡分析
- **跟随精度**: 平均偏差的倒数
- **稳定性**: 力量输出的一致性
- **反应时间**: 对目标变化的响应延迟
- **容差内时间**: 在可接受范围内的时间比例

### 详细指标
- **最大偏差**: 单次最大偏离值
- **平均偏差**: 整个关卡的平均偏差
- **力量范围**: 使用的力量范围
- **目标跟随率**: 跟随目标方向的成功率

## 数据导出

### 文件格式
- **JSON格式**: 完整的会话数据
- **CSV格式**: 便于统计分析的表格数据

### 数据内容
- 参与者信息和会话元数据
- MVC和REST校准值
- 每个关卡的详细数据点
- 性能指标和评分结果

### 存储位置
- **Android**: `/storage/emulated/0/Android/data/[包名]/files/ForceGameData/`
- **Windows**: `%USERPROFILE%/AppData/LocalLow/[公司名]/[产品名]/ForceGameData/`

## 故障排除

### 常见问题

#### 1. 设备连接失败
**症状**: 无法连接到握力计
**解决方案**:
- 检查握力计是否开启
- 确认设备MAC地址正确
- 重启蓝牙服务
- 检查应用权限设置

#### 2. 数据不稳定
**症状**: 力量数据跳动剧烈
**解决方案**:
- 调整数据平滑参数
- 检查握力计电量
- 确保握力计位置稳定
- 重新校准设备

#### 3. 游戏卡顿
**症状**: 界面响应缓慢
**解决方案**:
- 关闭其他应用程序
- 降低粒子特效质量
- 检查设备性能
- 重启应用

#### 4. 数据丢失
**症状**: 实验数据未保存
**解决方案**:
- 检查存储权限
- 确保有足够存储空间
- 启用自动保存功能
- 手动保存数据

### 调试功能

#### Editor模式
- 自动启用数据模拟
- 跳过设备连接步骤
- 提供调试控制面板

#### 日志系统
- 详细的运行日志
- 错误信息记录
- 性能监控数据

#### 测试功能
- 模拟握力输入
- 快速关卡切换
- 数据导出测试

## 最佳实践

### 实验准备
1. 确保握力计充满电
2. 测试设备连接稳定性
3. 准备充足的存储空间
4. 关闭不必要的后台应用

### 数据质量
1. 进行充分的校准
2. 保持握力计位置稳定
3. 避免外界干扰
4. 定期检查数据完整性

### 用户体验
1. 提供清晰的操作指导
2. 及时给予反馈
3. 合理设置休息时间
4. 记录用户反馈

## 技术支持

### 联系方式
- 技术文档: `Assets/Docs/`
- 问题反馈: 通过项目仓库提交Issue
- 开发团队: 查看项目贡献者列表

### 更新日志
- 版本历史记录在 `CHANGELOG.md`
- 新功能和修复说明
- 兼容性信息

### 开发资源
- Unity官方文档
- BLE开发指南
- 数据分析工具推荐

---

**注意**: 本系统仅用于科研目的，请确保符合相关伦理规范和数据保护要求。
