using System;
using System.Collections.Generic;
using UnityEngine;

namespace ForceFollowingGame.Core
{
    /// <summary>
    /// 力量数据点
    /// </summary>
    [System.Serializable]
    public struct ForceDataPoint
    {
        public float timestamp;
        public float actualForce;
        public float targetForce;
        public float deviation;
        public bool isInTolerance;

        public ForceDataPoint(float timestamp, float actualForce, float targetForce, float toleranceThreshold = 10f)
        {
            this.timestamp = timestamp;
            this.actualForce = actualForce;
            this.targetForce = targetForce;
            this.deviation = Mathf.Abs(actualForce - targetForce);
            this.isInTolerance = this.deviation <= toleranceThreshold;
        }
    }

    /// <summary>
    /// 性能指标
    /// </summary>
    [System.Serializable]
    public struct PerformanceMetrics
    {
        public float accuracy;          // 跟随精度 (0-100%)
        public float consistency;       // 一致性/稳定性 (0-100%)
        public float responseTime;      // 平均反应时间 (ms)
        public float finalScore;        // 最终得分 (0-1000+)
        public int stars;              // 星级评定 (0-3)
        public float duration;         // 完成时间
        public Dictionary<string, float> detailedMetrics; // 详细指标

        public PerformanceMetrics(float accuracy, float consistency, float responseTime, 
            float finalScore, int stars, float duration)
        {
            this.accuracy = accuracy;
            this.consistency = consistency;
            this.responseTime = responseTime;
            this.finalScore = finalScore;
            this.stars = stars;
            this.duration = duration;
            this.detailedMetrics = new Dictionary<string, float>();
        }
    }

    /// <summary>
    /// 轨迹类型枚举
    /// </summary>
    public enum TrajectoryType
    {
        SteadyHold,     // 稳定保持
        SlowRamp,       // 缓慢上升
        FastRamp,       // 快速上升  
        Wave,           // 正弦波
        Steps,          // 阶梯状
        Random,         // 随机波动
        Challenge       // 自适应挑战
    }

    /// <summary>
    /// 游戏关卡配置
    /// </summary>
    [System.Serializable]
    public class GameLevel
    {
        [Header("基本信息")]
        public int levelNumber;
        public string levelName;
        public string description;

        [Header("游戏参数")]
        public float baseMVCPercent;      // 基础MVC百分比
        public TrajectoryType trajectory; // 轨迹类型
        public float duration;            // 关卡时长(秒)
        public float difficulty;          // 难度系数(0-1)

        [Header("轨迹配置")]
        public AnimationCurve targetCurve; // 目标轨迹曲线
        public float toleranceThreshold = 10f; // 容差阈值

        [Header("视觉配置")]
        public Color levelColor = Color.white;
        public Sprite levelIcon;

        public GameLevel()
        {
            levelNumber = 1;
            levelName = "默认关卡";
            description = "默认描述";
            baseMVCPercent = 10f;
            trajectory = TrajectoryType.SteadyHold;
            duration = 30f;
            difficulty = 0.5f;
            targetCurve = AnimationCurve.Linear(0, 0, 1, 1);
        }

        public GameLevel(int number, string name, string desc, float mvcPercent, 
            TrajectoryType type, float time, float diff)
        {
            levelNumber = number;
            levelName = name;
            description = desc;
            baseMVCPercent = mvcPercent;
            trajectory = type;
            duration = time;
            difficulty = diff;
            targetCurve = AnimationCurve.Linear(0, 0, 1, 1);
        }

        /// <summary>
        /// 获取指定时间点的目标力量值
        /// </summary>
        public float GetTargetForceAtTime(float normalizedTime)
        {
            return targetCurve.Evaluate(Mathf.Clamp01(normalizedTime)) * baseMVCPercent;
        }

        /// <summary>
        /// 检查是否在容差范围内
        /// </summary>
        public bool IsInTolerance(float actualForce, float normalizedTime)
        {
            float targetForce = GetTargetForceAtTime(normalizedTime);
            return Mathf.Abs(actualForce - targetForce) <= toleranceThreshold;
        }
    }

    /// <summary>
    /// 关卡结果
    /// </summary>
    [System.Serializable]
    public class LevelResult
    {
        public int levelNumber;
        public string levelName;
        public float duration;
        public PerformanceMetrics performance;
        public List<ForceDataPoint> rawData = new();
        public GameLevel levelConfig;
        public DateTime completionTime = DateTime.Now;
    }

    /// <summary>
    /// 设备信息
    /// </summary>
    [System.Serializable]
    public class DeviceInfo
    {
        public string deviceMAC = "";
        public string deviceModel;
        public string osVersion;
        public string appVersion;
        public float calibrationDate;
        
        // 添加初始化方法
        public void Initialize()
        {
            deviceModel = SystemInfo.deviceModel;
            osVersion = SystemInfo.operatingSystem;
            appVersion = Application.version;
            calibrationDate = Time.time;
            // deviceMAC 可能需要特殊处理，这里保持为空
        }
    }

    /// <summary>
    /// 实验会话
    /// </summary>
    [System.Serializable]
    public class ExperimentSession
    {
        public string participantId;
        public string sessionId;
        public DateTime startTime;
        public DateTime endTime;
        public float mvcValue;
        public float restValue;
        public List<LevelResult> levelResults;
        public DeviceInfo deviceInfo;
        public string appVersion;

        public ExperimentSession()
        {
            levelResults = new List<LevelResult>();
            deviceInfo = new DeviceInfo();
            // appVersion = Application.version;
            // startTime = DateTime.Now;
        }

        public ExperimentSession(string participantId)
        {
            this.participantId = participantId;
            sessionId = GenerateSessionId();
            levelResults = new List<LevelResult>();
            deviceInfo = new DeviceInfo();
            appVersion = Application.version;
            startTime = DateTime.Now;
        }

        private string GenerateSessionId()
        {
            return DateTime.Now.ToString("yyyyMMdd_HHmmss");
        }

        /// <summary>
        /// 添加关卡结果
        /// </summary>
        public void AddLevelResult(LevelResult result)
        {
            levelResults.Add(result);
        }

        /// <summary>
        /// 获取总游戏时长
        /// </summary>
        public float GetTotalDuration()
        {
            return (float)(endTime - startTime).TotalSeconds;
        }

        /// <summary>
        /// 获取平均表现
        /// </summary>
        public PerformanceMetrics GetAveragePerformance()
        {
            if (levelResults.Count == 0)
                return new PerformanceMetrics();

            float totalAccuracy = 0f;
            float totalConsistency = 0f;
            float totalResponseTime = 0f;
            float totalScore = 0f;
            int totalStars = 0;
            float totalDuration = 0f;

            foreach (var result in levelResults)
            {
                totalAccuracy += result.performance.accuracy;
                totalConsistency += result.performance.consistency;
                totalResponseTime += result.performance.responseTime;
                totalScore += result.performance.finalScore;
                totalStars += result.performance.stars;
                totalDuration += result.performance.duration;
            }

            int count = levelResults.Count;
            return new PerformanceMetrics(
                totalAccuracy / count,
                totalConsistency / count,
                totalResponseTime / count,
                totalScore / count,
                totalStars / count,
                totalDuration
            );
        }
    }

    /// <summary>
    /// 校准数据
    /// </summary>
    [System.Serializable]
    public class CalibrationData
    {
        public float mvcValue;
        public float restValue;
        public DateTime calibrationTime;
        public bool isValid;

        public CalibrationData()
        {
            mvcValue = 0f;
            restValue = 0f;
            calibrationTime = DateTime.Now;
            isValid = false;
        }

        public CalibrationData(float mvc, float rest)
        {
            mvcValue = mvc;
            restValue = rest;
            calibrationTime = DateTime.Now;
            isValid = mvc > rest && mvc > 0;
        }

        /// <summary>
        /// 将原始力量值转换为MVC百分比
        /// </summary>
        public float ConvertToMVCPercent(float rawForce)
        {
            if (!isValid || mvcValue <= restValue)
                return 0f;

            float adjustedForce = Mathf.Max(0, rawForce - restValue);
            float adjustedMVC = mvcValue - restValue;
            
            return (adjustedForce / adjustedMVC) * 100f;
        }

        /// <summary>
        /// 检查校准数据是否有效
        /// </summary>
        public bool ValidateCalibration()
        {
            isValid = mvcValue > restValue && mvcValue > 0 && restValue >= 0;
            return isValid;
        }
    }
}
