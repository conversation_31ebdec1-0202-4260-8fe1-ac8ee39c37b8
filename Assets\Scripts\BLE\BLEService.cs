using System.Collections.Generic;

/// <summary>
/// BLE 服务模型
/// </summary>
public class BLEService
{
    public string UUID { get; set; }
    public List<BLECharacteristic> Characteristics { get; set; } = new List<BLECharacteristic>();

    public BLEService(SimpleBLE.BLEServiceInfo info)
    {
        UUID = info.UUID;
        foreach (var charInfo in info.Characteristics)
        {
            Characteristics.Add(new BLECharacteristic(charInfo));
        }
    }
}