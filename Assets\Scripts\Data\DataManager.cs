using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using ForceFollowingGame.Core;

namespace ForceFollowingGame.Data
{
    /// <summary>
    /// 数据管理器 - 处理实验数据的存储、加载和导出
    /// </summary>
    public class DataManager : MonoBehaviour
    {
        public static DataManager Instance { get; private set; }

        [Header("当前会话")]
        [SerializeField] private ExperimentSession currentSession;
        [SerializeField] private string currentParticipantId;

        [Header("存储配置")]
        [SerializeField] private string dataFolderName = "ForceGameData";
        [SerializeField] private bool autoSave = true;
        [SerializeField] private float autoSaveInterval = 30f; // 自动保存间隔(秒)

        [Header("校准数据")]
        [SerializeField] private CalibrationData calibrationData;

        [Header("调试")]
        [SerializeField] private bool enableDebugLogs = true;

        private string saveDirectory;

        void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeDataStorage();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        void Start()
        {
            if (autoSave)
            {
                InvokeRepeating(nameof(AutoSave), autoSaveInterval, autoSaveInterval);
            }
        }

        /// <summary>
        /// 初始化数据存储
        /// </summary>
        private void InitializeDataStorage()
        {
            saveDirectory = Path.Combine(Application.persistentDataPath, dataFolderName);

            if (!Directory.Exists(saveDirectory))
            {
                Directory.CreateDirectory(saveDirectory);
            }

            calibrationData = new CalibrationData();

            if (enableDebugLogs)
                Debug.Log($"[DataManager] 数据存储目录: {saveDirectory}");
        }

        /// <summary>
        /// 开始新会话
        /// </summary>
        /// <param name="participantId">参与者ID</param>
        public void StartNewSession(string participantId)
        {
            currentParticipantId = participantId;

            currentSession = new ExperimentSession(participantId);
            currentSession.deviceInfo.Initialize();

            if (enableDebugLogs)
                Debug.Log($"[DataManager] 开始新会话: {participantId}");
        }

        /// <summary>
        /// 设置MVC值
        /// </summary>
        /// <param name="mvc">MVC值</param>
        public void SetMVCValue(float mvc)
        {
            if (currentSession != null)
                currentSession.mvcValue = mvc;

            calibrationData.mvcValue = mvc;
            calibrationData.ValidateCalibration();

            if (enableDebugLogs)
                Debug.Log($"[DataManager] 设置MVC值: {mvc:F2}");
        }

        /// <summary>
        /// 设置REST值
        /// </summary>
        /// <param name="rest">REST值</param>
        public void SetRESTValue(float rest)
        {
            if (currentSession != null)
                currentSession.restValue = rest;

            calibrationData.restValue = rest;
            calibrationData.ValidateCalibration();

            if (enableDebugLogs)
                Debug.Log($"[DataManager] 设置REST值: {rest:F2}");
        }

        /// <summary>
        /// 保存关卡结果
        /// </summary>
        public void SaveLevelResult(int levelNumber, string levelName,
            PerformanceMetrics performance, List<ForceDataPoint> rawData, GameLevel levelConfig)
        {
            if (currentSession == null)
            {
                Debug.LogWarning("[DataManager] 当前会话为空，无法保存关卡结果");
                return;
            }

            var levelResult = new LevelResult
            {
                levelNumber = levelNumber,
                levelName = levelName,
                duration = performance.duration,
                performance = performance,
                rawData = new List<ForceDataPoint>(rawData), // 深拷贝
                levelConfig = levelConfig,
                completionTime = DateTime.Now
            };

            currentSession.AddLevelResult(levelResult);

            if (enableDebugLogs)
                Debug.Log($"[DataManager] 保存关卡结果: {levelName}, 得分: {performance.finalScore:F0}");

            if (autoSave)
            {
                AutoSave();
            }
        }

        /// <summary>
        /// 保存当前会话
        /// </summary>
        public void SaveCurrentSession()
        {
            if (currentSession == null)
            {
                Debug.LogWarning("[DataManager] 当前会话为空，无法保存");
                return;
            }

            currentSession.endTime = DateTime.Now;

            string fileName = $"{currentSession.participantId}_{currentSession.sessionId}.json";
            string filePath = Path.Combine(saveDirectory, fileName);

            try
            {
                string jsonData = JsonUtility.ToJson(currentSession, true);
                File.WriteAllText(filePath, jsonData);

                if (enableDebugLogs)
                    Debug.Log($"[DataManager] 会话数据已保存到: {filePath}");

                // 同时保存CSV格式（便于分析）
                SaveAsCSV(currentSession, filePath.Replace(".json", ".csv"));
            }
            catch (Exception e)
            {
                Debug.LogError($"[DataManager] 保存会话数据失败: {e.Message}");
            }
        }

        /// <summary>
        /// 保存为CSV格式
        /// </summary>
        private void SaveAsCSV(ExperimentSession session, string csvPath)
        {
            try
            {
                using (var writer = new StreamWriter(csvPath))
                {
                    // 写入头部信息
                    writer.WriteLine($"# 参与者ID: {session.participantId}");
                    writer.WriteLine($"# 会话ID: {session.sessionId}");
                    writer.WriteLine($"# 开始时间: {session.startTime}");
                    writer.WriteLine($"# 结束时间: {session.endTime}");
                    writer.WriteLine($"# MVC值: {session.mvcValue}");
                    writer.WriteLine($"# REST值: {session.restValue}");
                    writer.WriteLine();

                    // 写入CSV头
                    writer.WriteLine("LevelNumber,LevelName,Timestamp,ActualForce,TargetForce,Deviation,InTolerance,EventMarker");

                    // 写入每个关卡的数据
                    foreach (var level in session.levelResults)
                    {
                        foreach (var dataPoint in level.rawData)
                        {
                            writer.WriteLine($"{level.levelNumber},{level.levelName}," +
                                $"{dataPoint.timestamp:F3},{dataPoint.actualForce:F3}," +
                                $"{dataPoint.targetForce:F3},{dataPoint.deviation:F3}," +
                                $"{dataPoint.isInTolerance},{GetEventMarker(dataPoint, level)}");
                        }
                    }
                }

                if (enableDebugLogs)
                    Debug.Log($"[DataManager] CSV文件已保存到: {csvPath}");
            }
            catch (Exception e)
            {
                Debug.LogError($"[DataManager] 保存CSV文件失败: {e.Message}");
            }
        }

        /// <summary>
        /// 获取事件标记
        /// </summary>
        private string GetEventMarker(ForceDataPoint dataPoint, LevelResult level)
        {
            float normalizedTime = dataPoint.timestamp / level.duration;

            if (normalizedTime < 0.1f) return "START";
            if (normalizedTime > 0.9f) return "END";
            if (normalizedTime > 0.2f && normalizedTime < 0.8f) return "PLATEAU";
            return "TRANSITION";
        }

        /// <summary>
        /// 自动保存
        /// </summary>
        private void AutoSave()
        {
            if (currentSession != null && currentSession.levelResults.Count > 0)
            {
                SaveCurrentSession();
            }
        }

        /// <summary>
        /// 获取可用的参与者列表
        /// </summary>
        public List<string> GetAvailableParticipants()
        {
            var participants = new HashSet<string>();

            if (Directory.Exists(saveDirectory))
            {
                var files = Directory.GetFiles(saveDirectory, "*.json");

                foreach (var file in files)
                {
                    try
                    {
                        string jsonContent = File.ReadAllText(file);
                        var session = JsonUtility.FromJson<ExperimentSession>(jsonContent);
                        if (session != null)
                        {
                            participants.Add(session.participantId);
                        }
                    }
                    catch (Exception e)
                    {
                        Debug.LogWarning($"[DataManager] 读取文件失败 {file}: {e.Message}");
                    }
                }
            }

            return new List<string>(participants);
        }

        /// <summary>
        /// 获取参与者的所有会话
        /// </summary>
        public List<ExperimentSession> GetParticipantSessions(string participantId)
        {
            var sessions = new List<ExperimentSession>();

            if (Directory.Exists(saveDirectory))
            {
                var files = Directory.GetFiles(saveDirectory, "*.json");

                foreach (var file in files)
                {
                    try
                    {
                        string jsonContent = File.ReadAllText(file);
                        var session = JsonUtility.FromJson<ExperimentSession>(jsonContent);
                        if (session != null && session.participantId == participantId)
                        {
                            sessions.Add(session);
                        }
                    }
                    catch (Exception e)
                    {
                        Debug.LogWarning($"[DataManager] 读取文件失败 {file}: {e.Message}");
                    }
                }
            }

            return sessions;
        }

        /// <summary>
        /// 获取当前会话
        /// </summary>
        public ExperimentSession GetCurrentSession()
        {
            return currentSession;
        }

        /// <summary>
        /// 获取校准数据
        /// </summary>
        public CalibrationData GetCalibrationData()
        {
            return calibrationData;
        }

        /// <summary>
        /// 将原始力量值转换为MVC百分比
        /// </summary>
        public float ConvertToMVCPercent(float rawForce)
        {
            return calibrationData.ConvertToMVCPercent(rawForce);
        }

        /// <summary>
        /// 检查校准是否有效
        /// </summary>
        public bool IsCalibrationValid()
        {
            return calibrationData.isValid;
        }

        /// <summary>
        /// 获取数据统计信息
        /// </summary>
        public string GetDataStats()
        {
            if (currentSession == null)
                return "无活动会话";

            return $"参与者: {currentSession.participantId}, " +
                   $"关卡数: {currentSession.levelResults.Count}, " +
                   $"MVC: {currentSession.mvcValue:F1}, " +
                   $"REST: {currentSession.restValue:F1}";
        }

        /// <summary>
        /// 清理旧数据文件
        /// </summary>
        public void CleanupOldData(int daysToKeep = 30)
        {
            if (!Directory.Exists(saveDirectory)) return;

            var files = Directory.GetFiles(saveDirectory);
            var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
            int deletedCount = 0;

            foreach (var file in files)
            {
                var fileInfo = new FileInfo(file);
                if (fileInfo.CreationTime < cutoffDate)
                {
                    try
                    {
                        File.Delete(file);
                        deletedCount++;
                    }
                    catch (Exception e)
                    {
                        Debug.LogWarning($"[DataManager] 删除文件失败 {file}: {e.Message}");
                    }
                }
            }

            if (enableDebugLogs && deletedCount > 0)
                Debug.Log($"[DataManager] 清理了 {deletedCount} 个旧数据文件");
        }

        // 调试方法
        [ContextMenu("Print Data Stats")]
        public void DebugPrintStats()
        {
            Debug.Log($"[DataManager] {GetDataStats()}");
        }

        [ContextMenu("Save Current Session")]
        public void DebugSaveSession()
        {
            SaveCurrentSession();
        }

        [ContextMenu("Cleanup Old Data")]
        public void DebugCleanupData()
        {
            CleanupOldData();
        }

        void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                SaveCurrentSession();
            }
        }

        // void OnApplicationFocus(bool hasFocus)
        // {
        //     if (!hasFocus)
        //     {
        //         SaveCurrentSession();
        //     }
        // }

        void OnDestroy()
        {
            SaveCurrentSession();
        }
    }
}
