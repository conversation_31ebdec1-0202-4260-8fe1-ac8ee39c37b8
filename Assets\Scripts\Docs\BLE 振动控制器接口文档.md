# BLE 振动控制器接口文档

## 1. 概述

本文档描述了基于 ESP32 的蓝牙低功耗 (BLE)
振动控制器的接口规范，该设备支持通过蓝牙远程控制振动器的开关、强度和频率。

## 2. 设备信息

-   **设备名称**: vibrator_l

-   **服务 UUID**: 12a59900-17cc-11ec-9621-0242ac130002

-   **接收特征 UUID**: 12a59e0a-17cc-11ec-9621-0242ac130002 (WRITE 权限)

## 3. 控制命令

所有命令均为 ASCII 字符串格式，通过 WRITE 特征发送到设备。

### 3.1 工作模式控制

  ----------------------- ----------------------- ------------------------------------------------------------
命令                    参数                    描述

1                       无                      开启有效刺激模式，产生标准振动

2                       无                      停止所有振动输出

3                       无                      开启无效刺激模式，产生不同于有效刺激的振动模式（保留功能）
  ----------------------- ----------------------- ------------------------------------------------------------

### 3.2 振动强度控制

  ----------------------- ----------------------- -----------------------
命令                    参数范围                描述

S \[值\]                0-1023                  设置振动强度，0
为最弱，1023 为最强
  ----------------------- ----------------------- -----------------------

**示例**:

-   [S512]{.mark} - 设置强度为 50%

-   [S1023]{.mark} - 设置最大强度

### 3.3 振动频率控制

  ----------------------- ----------------------- --------------------------
命令                    参数范围                描述

F \[值\]                20-200 Hz               设置振动频率，单位为赫兹
(Hz)
  ----------------------- ----------------------- --------------------------

**示例**:

-   [F50]{.mark} - 设置频率为 50Hz

-   [F150]{.mark} - 设置频率为 150Hz

## 4. 技术规格

-   **电源**: 3.7V DC

-   **通信协议**: BLE GATT

-   **PWM 分辨率**: 10 位 (0-1023)

-   **频率范围**: 20-200Hz

-   **默认参数**:

    -   强度：约 97.7% (1000/1023)

    -   频率: 100Hz

    -   工作模式：关闭

## 5. 使用注意事项

1.  频率设置超出 20-200Hz 范围将被拒绝

2.  强度设置超出 0-1023 范围将被拒绝

3.  实际振动幅度受振动频率影响，建议动态调整

4.  当设备断开连接时，会自动停止振动输出

5.  重新连接后需重新发送控制命令

6.  长时间振动容易发烫